/**
 * tabulator_xml_func.js
 * Ch<PERSON>a các hàm tiện ích và biến cố định cho module XML4750
 */

// Biến toàn cục để lưu trữ dữ liệu danh mục
var categoryData = {};

const gioi_tinh = {"1": "Nam", "2": "Nữ", "3": "Chưa xác định"}       //Giới tính cho combobox
// Mã loại khám chữa bệnh được quy định theo quyết định 824/QĐ-BYT
const ma_loai_kcb = {"01":"Khám bệnh.","02":"Điều trị ngoại trú.","03":"Điều trị nội trú.","04":"Điều trị nội trú ban ngày.",
    "05":"Điều trị ngoại trú các bệnh mạn tính dài ngày liên tục trong năm, c<PERSON> kh<PERSON>m bệnh và lĩnh thuốc.",
    "06":"Điều trị lưu tại Trạm Y tế tuyến xã, Phòng khám đa khoa khu vực.","07":"Nhận thuốc theo hẹn (không khám bệnh).",
    "08":"Điều trị ngoại trú các bệnh mạn tính dài ngày liên tục trong năm, có thực hiện các dịch vụ kỹ thuật và/hoặc được sử dụng thuốc.",
    "09":"Điều trị nội trú dưới 04 (bốn) giờ.",
    "10":"Các trường hợp khác."}

// Định nghĩa mã đối tượng
const ma_doi_tuong = {
    "01": "Đúng tuyến", "1": "Đúng tuyến", "1.1": "Đúng tuyến ban đầu", "1.2": "Đúng tuyến tại tỉnh",
    "1.3": "Chuyển tuyến", "1.4": "Công tác, làm việc lưu động", "1.5": "Tái khám", "1.6": "Hiến tạng",
    "1.7": "Trẻ sơ sinh", "1.8": "Bệnh lao", "1.9": "HIV/AIDS", "1.10": "COVID-19", "02": "Cấp cứu",
    "2": "Cấp cứu", "03": "Trái tuyến", "3": "Trái tuyến", "3.1": "Trái tuyến TW", "3.2": "Trái tuyến Tỉnh",
    "3.3": "Trái tuyến tuyến huyện", "3.4": "Trái tuyến ngoại trú TW", "3.5": "Trái tuyến ngoại trú Tỉnh",
    "3.6": "Dân tộc, vùng khó khăn", "3.7": "Còn lại",
    "7": "Lĩnh thuốc theo giấy hẹn trong trường hợp dịch bệnh hoặc bất khả kháng hoặc do bất kỳ nguyên nhân nào",
    "7.1": "Người bệnh đến cơ sở khám bệnh, chữa bệnh lĩnh thuốc.",
    "7.2": "Người bệnh ủy quyền cho người khác đến cơ sở khám bệnh, chữa bệnh lĩnh thuốc.",
    "7.3": "Cơ sở khám bệnh, chữa bệnh chuyển thuốc cho cơ sở khám bệnh, chữa bệnh khác.",
    "7.4": "Cơ sở khám bệnh, chữa bệnh chuyển thuốc đến cho người bệnh.",
    "8": "Thu hồi đề nghị thanh toán",
    "9": "Khám bệnh, chữa bệnh dịch vụ"
};

ma_dan_toc = {'01': 'Kinh ','02': 'Tày','03': 'Thái','04': 'Hoa ','05': 'Khmer','06': 'Mường ','07': 'Nùng  ','08': 'Mông','09': 'Dao','10': 'Gia Rai','11': 'Ngái','12': 'Ê  Đê','13': 'Ba Na','14': 'Xơ Đăng','15': 'Sán Chay ','16': 'Cơ Ho','17': 'Chăm ','18': 'Sán Dìu','19': 'Hrê','20': 'Mnông','21': 'Raglay','22': 'Xtiêng','23': 'Bru Vân Kiều','24': 'Thổ (4)','25': 'Giáy','26': 'Cơ Tu','27': 'Gié Triêng','28': 'Mạ','29': 'Khơ mú','30': 'Co','31': 'Tà Ôi','32': 'Chơ Ro','33': 'Kháng','34': 'Xinh Mun','35': 'Hà Nhì','36': 'Chu Ru','37': 'Lào','38': 'La Chí','39': 'La Ha','40': 'Phù Lá','41': 'La Hủ','42': 'Lự','43': 'Lô Lô','44': 'Chứt','45': 'Mảng','46': 'Pà Thẻn','47': 'Cơ Lao','48': 'Cống','49': 'Bố Y','50': 'Si La','51': 'Pu Péo','52': 'Brâu','53': 'Ơ Đu','54': 'Rơ Măm','55': 'Người nước ngoài','56': 'Không xác định'}

// Giới tính đã được định nghĩa ở trên

// Hàm load dữ liệu danh mục
function loadCategoryData(categories, formats) {
    var promises = [];

    // Tạo promise cho mỗi danh mục cần load
    categories.forEach(function(category, index) {
        // Lấy định dạng tương ứng nếu có
        var format = formats && formats[category] ? formats[category] : 'ma_diengiai';

        var promise = $.ajax({
            url: API_CATEGORY_DATA_URL || "/danhmuc130/api_category_data/",
            type: "GET",
            data: {
                category: category,
                active_only: true,
                format: format
            },
            dataType: "json",
            cache: true
        }).done(function(response) {
            if (response.success) {
                // Lưu dữ liệu vào biến toàn cục
                categoryData[category] = response.data;
            } else {
                console.error("Lỗi khi lấy danh sách " + category + ":", response.error);
                categoryData[category] = [];
            }
        }).fail(function(xhr, status, error) {
            console.error("Lỗi AJAX khi load " + category + ":", error);
            categoryData[category] = [];
        });

        promises.push(promise);
    });

    // Trả về promise tổng hợp
    return $.when.apply($, promises);
}

// Hàm lấy dữ liệu danh mục đã load
function getCategoryValues(category) {
    // Trả về dữ liệu dưới dạng object với key là value và value là label
    // Đây là định dạng mà Tabulator mong đợi cho editor "list"
    var values = {};
    var data = categoryData[category] || [];

    data.forEach(function(item) {
        values[item.value] = item.label;
    });

    return values;
}

// Custom datetime editor: sử dụng <input type="datetime-local">
const dateTimeEditor = function(cell, onRendered, success, cancel, editorParams){
    const editor = document.createElement("input");
    editor.setAttribute("type", "datetime-local");

    editor.style.padding = "3px";
    editor.style.width = "100%";
    editor.style.boxSizing = "border-box";

    const originalValue = cell.getValue(); // ví dụ: "200012251230"

    // Chuyển từ yyyyMMddHHmm -> yyyy-MM-dd'T'HH:mm (định dạng dùng cho input)
    const parsed = luxon.DateTime.fromFormat(originalValue, "yyyyMMddHHmm");
    editor.value = parsed.isValid ? parsed.toFormat("yyyy-MM-dd'T'HH:mm") : "";

    onRendered(() => {
        editor.focus();
    });

    function successFunc(){
        const selected = editor.value; // ví dụ: "2000-12-25T12:30"
        if (selected) {
            const dt = luxon.DateTime.fromFormat(selected, "yyyy-MM-dd'T'HH:mm");
            success(dt.toFormat("yyyyMMddHHmm")); // ghi lại định dạng CSDL
        } else {
            success("");
        }
    }

    editor.addEventListener("change", successFunc);
    editor.addEventListener("blur", successFunc);

    return editor;
};

// Custom date editor
const dateEditor = function(cell, onRendered, success, cancel, editorParams){
    const editor = document.createElement("input");
    editor.setAttribute("type", "date");

    editor.style.padding = "3px";
    editor.style.width = "100%";
    editor.style.boxSizing = "border-box";

    const originalValue = cell.getValue(); // ví dụ: "20001225"

    // Chuyển từ yyyyMMdd -> yyyy-MM-dd (định dạng dùng cho input)
    const parsed = luxon.DateTime.fromFormat(originalValue, "yyyyMMdd");
    editor.value = parsed.isValid ? parsed.toFormat("yyyy-MM-dd") : "";

    onRendered(() => {
        editor.focus();
    });

    function successFunc(){
        const selected = editor.value; // ví dụ: "2000-12-25T12:30"
        if (selected) {
            const dt = luxon.DateTime.fromFormat(selected, "yyyy-MM-dd");
            success(dt.toFormat("yyyyMMdd")); // ghi lại định dạng CSDL
        } else {
            success("");
        }
    }

    editor.addEventListener("change", successFunc);
    editor.addEventListener("blur", successFunc);

    return editor;
};

// Hàm formatter date time dùng chung
function formatDateTime(cell) {
    const raw = cell.getValue(); // ví dụ: "202405221430"
    const dt = luxon.DateTime.fromFormat(raw, "yyyyMMddHHmm");
    return dt.isValid ? dt.toFormat("dd/MM/yyyy HH:mm") : "";
}

// Hàm formatter date dùng chung
function formatDate(cell) {
    const raw = cell.getValue(); // ví dụ: "20240522"
    const dt = luxon.DateTime.fromFormat(raw, "yyyyMMdd");
    return dt.isValid ? dt.toFormat("dd/MM/yyyy") : "";
}

// Hàm formatter cho các trường key + value
function formatKeyValue(dict) {
    return function(cell) {
        const value = cell.getValue();
        const label = dict[value] || "(Không xác định)";
        return `${value} - ${label}`;
    };
}

function safeTextFormatter(cell) {
    let text = cell.getValue();
    if (!text) return '';
    let div = document.createElement('div');
    div.innerText = text;
    return div.innerHTML.replace(/\n/g, '<br>');
}
// Function to handle saving edited cell data via AJAX
function saveEditedCell(cell) {
    var rowData = cell.getRow().getData();
    var field = cell.getColumn().getField();
    var value = cell.getValue();
    var xmlKey = cell.getTable().element.id.replace('-table', '');

    console.log("Saving cell edit for", xmlKey, ":", field, "=", value);
    console.log("Full row data:", rowData);

    // Lấy CSRF token từ cookie
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    const csrftoken = getCookie('csrftoken');

    // Gửi AJAX request để lưu dữ liệu
    $.ajax({
        url: '/xml4750/update_field/',
        method: 'POST',
        data: {
            xml_type: xmlKey.toUpperCase(),
            row_id: rowData.id,
            field: field,
            value: value
        },
        headers: {
            'X-CSRFToken': csrftoken
        },
        success: function(response) {
            if (response.success) {
                console.log('Cập nhật thành công:', response);
                // Hiển thị thông báo thành công
                Swal.fire({
                    icon: 'success',
                    title: 'Thành công',
                    text: 'Dữ liệu đã được cập nhật',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000
                });
            } else {
                console.error('Cập nhật thất bại:', response);
                // Hiển thị thông báo lỗi
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi',
                    text: response.message || 'Đã xảy ra lỗi khi cập nhật dữ liệu',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('Lỗi AJAX:', error);
            // Hiển thị thông báo lỗi
            Swal.fire({
                icon: 'error',
                title: 'Lỗi',
                text: 'Đã xảy ra lỗi khi cập nhật dữ liệu: ' + error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
        }
    });
}
