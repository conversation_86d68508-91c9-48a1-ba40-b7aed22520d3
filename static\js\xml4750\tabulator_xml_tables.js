/**
 * tabulator_xml_tables.js
 * Ch<PERSON>a các định nghĩa bảng và cấu hình cột cho module XML4750
 */

// Hàm chuyển đổi dữ liệu từ Django sang định dạng cho Tabulator
function convertDjangoDataToTabulator(xmlType, djangoData) {
    var data = [];

    // Lấy dữ liệu từ biến Django được truyền vào
    switch(xmlType) {
        case 'XML0':
            if (djangoData && djangoData.xml0_list) {
                djangoData.xml0_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        maBN: item.maBN || '',
                        hoTen: item.hoTen || '',
                        soCCCD: item.soCCCD || '',
                        ngaySinh: item.ngaySinh || '',
                        gioiTinh: item.gioiTinh || '',
                        maTheBHYT: item.maTheBHYT || '',
                        maDKBD: item.maDKBD || '',
                        gtTheTu: item.gtTheTu || '',
                        gtTheDen: item.gtTheDen || '',
                        maDoiTuongKCB: item.maDoiTuongKCB || '',
                        ngayVao: item.ngayVao || '',
                        ngayVaoNoiTru: item.ngayVaoNoiTru || '',
                        ly_do_vnt: item.ly_do_vnt || '',
                        ma_ly_do_vnt: item.ma_ly_do_vnt || '',
                        maLoaiKCB: item.maLoaiKCB || '',
                        maCSKCB: item.maCSKCB || '',
                        maDichVu: item.maDichVu || '',
                        tenDichVu: item.tenDichVu || '',
                        ma_thuoc: item.ma_thuoc || '',
                        tenThuoc: item.tenThuoc || '',
                        maVatTu: item.maVatTu || '',
                        tenVatTu: item.tenVatTu || '',
                        ngayYL: item.ngayYL || '',
                        duPhong: item.duPhong || ''
                    });
                });
            }
            break;

        case 'XML1':
            if (djangoData && djangoData.xml1_list) {
                djangoData.xml1_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        maBN: item.maBN || '',
                        hoTen: item.hoTen || '',
                        soCCCD: item.soCCCD || '',
                        ngaySinh: item.ngaySinh || '',
                        gioiTinh: item.gioiTinh || '',
                        nhomMau: item.nhomMau || '',
                        maQuocTich: item.maQuocTich || '',
                        maDanToc: item.maDanToc || '',
                        maNgheNghiep: item.maNgheNghiep || '',
                        diaChi: item.diaChi || '',
                        maTinhCuTru: item.maTinhCuTru || '',
                        maHuyenCuTru: item.maHuyenCuTru || '',
                        maXaCuTru: item.maXaCuTru || '',
                        dienThoai: item.dienThoai || '',
                        maTheBHYT: item.maTheBHYT || '',
                        maDKBD: item.maDKBD || '',
                        gtTheTu: item.gtTheTu || '',
                        gtTheDen: item.gtTheDen || '',
                        ngayMienCCT: item.ngayMienCCT || '',
                        lyDoVV: item.lyDoVV || '',
                        lyDoVNT: item.lyDoVNT || '',
                        maLyDoVNT: item.maLyDoVNT || '',
                        chanDoanVao: item.chanDoanVao || '',
                        chanDoanRV: item.chanDoanRV || '',
                        maBenhChinh: item.maBenhChinh || '',
                        maBenhKT: item.maBenhKT || '',
                        maBenhYHCT: item.maBenhYHCT || '',
                        maPTTTQT: item.maPTTTQT || '',
                        maDoiTuongKCB: item.maDoiTuongKCB || '',
                        maNoiDi: item.maNoiDi || '',
                        maNoiDen: item.maNoiDen || '',
                        maTaiNan: item.maTaiNan || '',
                        ngayVao: item.ngayVao || '',
                        ngayVaoNoiTru: item.ngayVaoNoiTru || '',
                        ngayRa: item.ngayRa || '',
                        giayChuyenTuyen: item.giayChuyenTuyen || '',
                        soNgayDtri: item.soNgayDtri || '',
                        ppDieuTri: item.ppDieuTri || '',
                        ketQuaDtri: item.ketQuaDtri || '',
                        maLoaiRV: item.maLoaiRV || '',
                        ghiChu: item.ghiChu || '',
                        ngayTToan: item.ngayTToan || '',
                        tThuoc: item.tThuoc || '',
                        tVTYT: item.tVTYT || '',
                        tTongChiBV: item.tTongChiBV || '',
                        tTongChiBH: item.tTongChiBH || '',
                        tBNTT: item.tBNTT || '',
                        tBNCCT: item.tBNCCT || '',
                        tBHTT: item.tBHTT || '',
                        tNguonKhac: item.tNguonKhac || '',
                        tBHTTGDV: item.tBHTTGDV || '',
                        namQT: item.namQT || '',
                        thangQT: item.thangQT || '',
                        maLoaiKCB: item.maLoaiKCB || '',
                        maKhoa: item.maKhoa || '',
                        maCSKCB: item.maCSKCB || '',
                        maKhuVuc: item.maKhuVuc || '',
                        canNang: item.canNang || '',
                        canNangCon: item.canNangCon || '',
                        namNamLienTuc: item.namNamLienTuc || '',
                        ngayTaiKham: item.ngayTaiKham || '',
                        maHSBA: item.maHSBA || '',
                        maTTDV: item.maTTDV || '',
                        duPhong: item.duPhong || '',
                        ngayTao: item.ngayTao || '',
                        ngayChinhSua: item.ngayChinhSua || '',
                        trangThaiGuiBHXH: item.trangThaiGuiBHXH || '',
                        maTinh: item.maTinh || ''
                    });
                });
            }
            break;

        case 'XML2':
            if (djangoData && djangoData.xml2_list) {
                djangoData.xml2_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        stt: item.stt || '',
                        maThuoc: item.maThuoc || '',
                        maPPCheBien: item.maPPCheBien || '',
                        maCSKCBThuoc: item.maCSKCBThuoc || '',
                        maNhom: item.maNhom || '',
                        tenThuoc: item.tenThuoc || '',
                        donViTinh: item.donViTinh || '',
                        hamLuong: item.hamLuong || '',
                        duongDung: item.duongDung || '',
                        dangBaoChe: item.dangBaoChe || '',
                        lieuDung: item.lieuDung || '',
                        cachDung: item.cachDung || '',
                        soDangKy: item.soDangKy || '',
                        ttThau: item.ttThau || '',
                        phamVi: item.phamVi || '',
                        tyLeTTBH: item.tyLeTTBH || '',
                        soLuong: item.soLuong || '',
                        donGia: item.donGia || '',
                        thanhTienBV: item.thanhTienBV || '',
                        thanhTienBH: item.thanhTienBH || '',
                        tNguonKhacNSNN: item.tNguonKhacNSNN || '',
                        tNguonKhacVTNN: item.tNguonKhacVTNN || '',
                        tNguonKhacVTTN: item.tNguonKhacVTTN || '',
                        tNguonKhacCL: item.tNguonKhacCL || '',
                        tNguonKhac: item.tNguonKhac || '',
                        mucHuong: item.mucHuong || '',
                        tBNTT: item.tBNTT || '',
                        tBNCCT: item.tBNCCT || '',
                        tBHTT: item.tBHTT || '',
                        maKhoa: item.maKhoa || '',
                        maBacSi: item.maBacSi || '',
                        maDichVu: item.maDichVu || '',
                        ngayYL: item.ngayYL || '',
                        ngayTHYL: item.ngayTHYL || '',
                        maPTTT: item.maPTTT || '',
                        nguonCTra: item.nguonCTra || '',
                        vetThuongTP: item.vetThuongTP || '',
                        duPhong: item.duPhong || ''
                    });
                });
            }
            break;

        case 'XML3':
            if (djangoData && djangoData.xml3_list) {
                djangoData.xml3_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        stt: item.stt || '',
                        maDichVu: item.maDichVu || '',
                        maPTTTQT: item.maPTTTQT || '',
                        maVatTu: item.maVatTu || '',
                        maNhom: item.maNhom || '',
                        goiVTYT: item.goiVTYT || '',
                        tenVatTu: item.tenVatTu || '',
                        tenDichVu: item.tenDichVu || '',
                        maXangDau: item.maXangDau || '',
                        donViTinh: item.donViTinh || '',
                        phamVi: item.phamVi || '',
                        soLuong: item.soLuong || '',
                        donGiaBV: item.donGiaBV || '',
                        donGiaBH: item.donGiaBH || '',
                        tyLeTT: item.tyLeTT || '',
                        thanhTienBV: item.thanhTienBV || '',
                        thanhTienBH: item.thanhTienBH || '',
                        ttThau: item.ttThau || '',
                        tyLeTTDV: item.tyLeTTDV || '',
                        tyLeTTBH: item.tyLeTTBH || '',
                        tTranTT: item.tTranTT || '',
                        mucHuong: item.mucHuong || '',
                        tNguonKhacNSNN: item.tNguonKhacNSNN || '',
                        tNguonKhacVTNN: item.tNguonKhacVTNN || '',
                        tNguonKhacVTTN: item.tNguonKhacVTTN || '',
                        tNguonKhacCL: item.tNguonKhacCL || '',
                        tNguonKhac: item.tNguonKhac || '',
                        tBNTT: item.tBNTT || '',
                        tBNCCT: item.tBNCCT || '',
                        tBHTT: item.tBHTT || '',
                        maKhoa: item.maKhoa || '',
                        maGiuong: item.maGiuong || '',
                        maBacSi: item.maBacSi || '',
                        nguoiThucHien: item.nguoiThucHien || '',
                        maBenh: item.maBenh || '',
                        maBenhYHCT: item.maBenhYHCT || '',
                        ngayYL: item.ngayYL || '',
                        ngayTHYL: item.ngayTHYL || '',
                        ngayKQ: item.ngayKQ || '',
                        maPTTT: item.maPTTT || '',
                        vetThuongTP: item.vetThuongTP || '',
                        ppVoCam: item.ppVoCam || '',
                        viTriThDVKT: item.viTriThDVKT || '',
                        maMay: item.maMay || '',
                        maHieuSP: item.maHieuSP || '',
                        taiSuDung: item.taiSuDung || '',
                        duPhong: item.duPhong || ''
                    });
                });
            }
            break;

        case 'XML4':
            if (djangoData && djangoData.xml4_list) {
                djangoData.xml4_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        stt: item.stt || '',
                        maDichVu: item.maDichVu || '',
                        maChiSo: item.maChiSo || '',
                        tenChiSo: item.tenChiSo || '',
                        giaTri: item.giaTri || '',
                        donViDo: item.donViDo || '',
                        moTa: item.moTa || '',
                        ketLuan: item.ketLuan || '',
                        ngayKQ: item.ngayKQ || '',
                        maBSDocKQ: item.maBSDocKQ || '',
                        duPhong: item.duPhong || ''
                    });
                });
            }
            break;

        case 'XML5':
            if (djangoData && djangoData.xml5_list) {
                djangoData.xml5_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        stt: item.stt || '',
                        dienBienLS: item.dienBienLS || '',
                        giaiDoanBenh: item.giaiDoanBenh || '',
                        hoiChan: item.hoiChan || '',
                        phauThuat: item.phauThuat || '',
                        thoiDiemDBLS: item.thoiDiemDBLS || '',
                        nguoiThucHien: item.nguoiThucHien || '',
                        duPhong: item.duPhong || ''
                    });
                });
            }
            break;

        case 'XML6':
            if (djangoData && djangoData.xml6_list) {
                djangoData.xml6_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        maTheBHYT: item.maTheBHYT || '',
                        soCCCD: item.soCCCD || '',
                        ngaySinh: item.ngaySinh || '',
                        maTinhCuTru: item.maTinhCuTru || '',
                        maHuyenCuTru: item.maHuyenCuTru || '',
                        maXaCuTru: item.maXaCuTru || '',
                        ngayKDHIV: item.ngayKDHIV || '',
                        noiLayMauXN: item.noiLayMauXN || '',
                        noiXNKD: item.noiXNKD || '',
                        noiBDDTARV: item.noiBDDTARV || '',
                        maPhaDoDieuTriBD: item.maPhaDoDieuTriBD || '',
                        loaiDtriLao: item.loaiDtriLao || '',
                        phacDoDtriLao: item.phacDoDtriLao || '',
                        kqDTLao: item.kqDTLao || '',
                        ghiChu: item.ghiChu || ''
                    });
                });
            }
            break;

        case 'XML7':
            if (djangoData && djangoData.xml7_list) {
                djangoData.xml7_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        soLuuTru: item.soLuuTru || '',
                        maYTe: item.maYTe || '',
                        maKhoaRV: item.maKhoaRV || '',
                        ngayVao: item.ngayVao || '',
                        ngayRa: item.ngayRa || '',
                        maDinhChiThai: item.maDinhChiThai || '',
                        chanDoanRV: item.chanDoanRV || '',
                        ppDieuTri: item.ppDieuTri || '',
                        tuoiThai: item.tuoiThai || '',
                        nguyenNhanDinhChi: item.nguyenNhanDinhChi || '',
                        maCha: item.maCha || '',
                        maMe: item.maMe || '',
                        hoTenCha: item.hoTenCha || '',
                        hoTenMe: item.hoTenMe || '',
                        ghiChu: item.ghiChu || ''
                    });
                });
            }
            break;

        case 'XML8':
            if (djangoData && djangoData.xml8_list) {
                djangoData.xml8_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        hoTenCha: item.hoTenCha || '',
                        hoTenMe: item.hoTenMe || '',
                        nguoiGiamHo: item.nguoiGiamHo || '',
                        ngayVao: item.ngayVao || '',
                        ngayRa: item.ngayRa || '',
                        chanDoanVao: item.chanDoanVao || '',
                        chanDoanRV: item.chanDoanRV || '',
                        qtBenhLy: item.qtBenhLy || '',
                        tomTatKQ: item.tomTatKQ || '',
                        ngaySinhCon: item.ngaySinhCon || '',
                        ngayConChet: item.ngayConChet || '',
                        soConChet: item.soConChet || '',
                        ppDieuTri: item.ppDieuTri || '',
                        ketQuaDtri: item.ketQuaDtri || '',
                        ghiChu: item.ghiChu || ''
                    });
                });
            }
            break;

        case 'XML9':
            if (djangoData && djangoData.xml9_list) {
                djangoData.xml9_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        hotenNND: item.hotenNND || '',
                        ngaySinhNND: item.ngaySinhNND || '',
                        soCCCDNND: item.soCCCDNND || '',
                        noiCuTruNND: item.noiCuTruNND || '',
                        maTinhCuTru: item.maTinhCuTru || '',
                        maHuyenCuTru: item.maHuyenCuTru || '',
                        maXaCuTru: item.maXaCuTru || '',
                        hoTenCon: item.hoTenCon || '',
                        gioiTinhCon: item.gioiTinhCon || '',
                        soCon: item.soCon || '',
                        lanSinh: item.lanSinh || '',
                        canNangCon: item.canNangCon || '',
                        ngaySinhCon: item.ngaySinhCon || '',
                        noiSinhCon: item.noiSinhCon || '',
                        tinhTrangCon: item.tinhTrangCon || '',
                        sinhConPhauThuat: item.sinhConPhauThuat || '',
                        nguoiDoDe: item.nguoiDoDe || '',
                        ghiChu: item.ghiChu || ''
                    });
                });
            }
            break;

        case 'XML10':
            if (djangoData && djangoData.xml10_list) {
                djangoData.xml10_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        soSeri: item.soSeri || '',
                        soCT: item.soCT || '',
                        soNgay: item.soNgay || '',
                        donVi: item.donVi || '',
                        chanDoanRV: item.chanDoanRV || '',
                        tuNgay: item.tuNgay || '',
                        denNgay: item.denNgay || '',
                        maBS: item.maBS || '',
                        tenBS: item.tenBS || '',
                        ngayCT: item.ngayCT || '',
                        ghiChu: item.ghiChu || ''
                    });
                });
            }
            break;

        case 'XML11':
            if (djangoData && djangoData.xml11_list) {
                djangoData.xml11_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        soCT: item.soCT || '',
                        soSeri: item.soSeri || '',
                        soKCB: item.soKCB || '',
                        donVi: item.donVi || '',
                        maBHXH: item.maBHXH || '',
                        maTheBHYT: item.maTheBHYT || '',
                        chanDoanRV: item.chanDoanRV || '',
                        ppDieuTri: item.ppDieuTri || '',
                        maDinhChiThai: item.maDinhChiThai || '',
                        tuoiThai: item.tuoiThai || '',
                        nguyenNhanDinhChi: item.nguyenNhanDinhChi || '',
                        soNgayNghi: item.soNgayNghi || '',
                        tuNgay: item.tuNgay || '',
                        denNgay: item.denNgay || '',
                        ghiChu: item.ghiChu || ''
                    });
                });
            }
            break;

        case 'XML12':
            if (djangoData && djangoData.xml12_list) {
                djangoData.xml12_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        nguoiChuTri: item.nguoiChuTri || '',
                        chucVu: item.chucVu || '',
                        ngayHop: item.ngayHop || '',
                        hoTen: item.hoTen || '',
                        ngaySinh: item.ngaySinh || '',
                        soCCCD: item.soCCCD || '',
                        diaChi: item.diaChi || '',
                        maTinhCuTru: item.maTinhCuTru || '',
                        maHuyenCuTru: item.maHuyenCuTru || '',
                        maXaCuTru: item.maXaCuTru || '',
                        maBHXH: item.maBHXH || '',
                        maTheBHYT: item.maTheBHYT || '',
                        tyLeTTCTCu: item.tyLeTTCTCu || '',
                        tyLeTTCTMoi: item.tyLeTTCTMoi || '',
                        tongTyLeTTCT: item.tongTyLeTTCT || '',
                        ghiChu: item.ghiChu || ''
                    });
                });
            }
            break;

        case 'XML13':
            if (djangoData && djangoData.xml13_list) {
                djangoData.xml13_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        stt: item.stt || '',
                        maThuoc: item.maThuoc || '',
                        maNhom: item.maNhom || '',
                        tenThuoc: item.tenThuoc || '',
                        donViTinh: item.donViTinh || '',
                        hamLuong: item.hamLuong || '',
                        duongDung: item.duongDung || '',
                        lieuDung: item.lieuDung || '',
                        soLuong: item.soLuong || '',
                        donGiaBV: item.donGiaBV || '',
                        donGiaBH: item.donGiaBH || '',
                        tyLeTT: item.tyLeTT || '',
                        thanhTienBV: item.thanhTienBV || '',
                        thanhTienBH: item.thanhTienBH || '',
                        ttThau: item.ttThau || '',
                        tyLeTTBH: item.tyLeTTBH || '',
                        tTranTT: item.tTranTT || '',
                        mucHuong: item.mucHuong || '',
                        tNguonKhacNSNN: item.tNguonKhacNSNN || '',
                        tNguonKhacVTNN: item.tNguonKhacVTNN || '',
                        tNguonKhacVTTN: item.tNguonKhacVTTN || '',
                        tNguonKhacCL: item.tNguonKhacCL || '',
                        tNguonKhac: item.tNguonKhac || '',
                        tBNTT: item.tBNTT || '',
                        tBNCCT: item.tBNCCT || '',
                        tBHTT: item.tBHTT || '',
                        maKhoa: item.maKhoa || '',
                        maBacSi: item.maBacSi || '',
                        nguoiThucHien: item.nguoiThucHien || '',
                        maBenh: item.maBenh || '',
                        maBenhYHCT: item.maBenhYHCT || '',
                        ngayYL: item.ngayYL || '',
                        ngayDung: item.ngayDung || '',
                        maPTTT: item.maPTTT || '',
                        duPhong: item.duPhong || ''
                    });
                });
            }
            break;

        case 'XML14':
            if (djangoData && djangoData.xml14_list) {
                djangoData.xml14_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        soHoSo: item.soHoSo || '',
                        soChuyenTuyen: item.soChuyenTuyen || '',
                        giayChuyenTuyen: item.giayChuyenTuyen || '',
                        maCSKCB: item.maCSKCB || '',
                        maNoiDi: item.maNoiDi || '',
                        maNoiDen: item.maNoiDen || '',
                        hoTen: item.hoTen || '',
                        ngaySinh: item.ngaySinh || '',
                        gioiTinh: item.gioiTinh || '',
                        maTheBHYT: item.maTheBHYT || '',
                        gtTheDen: item.gtTheDen || '',
                        chanDoanRV: item.chanDoanRV || '',
                        qtBenhLy: item.qtBenhLy || '',
                        huongDieuTri: item.huongDieuTri || '',
                        maTTDV: item.maTTDV || '',
                        ngayCT: item.ngayCT || '',
                        maTheTam: item.maTheTam || '',
                        duPhong: item.duPhong || ''
                    });
                });
            }
            break;

        case 'XML15':
            if (djangoData && djangoData.xml15_list) {
                djangoData.xml15_list.forEach(function(item) {
                    data.push({
                        id: item.id,
                        maLK: item.maLK || '',
                        soPhieu: item.soPhieu || '',
                        hoTen: item.hoTen || '',
                        ngaySinh: item.ngaySinh || '',
                        gioiTinh: item.gioiTinh || '',
                        diaChi: item.diaChi || '',
                        maTheBHYT: item.maTheBHYT || '',
                        chanDoan: item.chanDoan || '',
                        ppDieuTri: item.ppDieuTri || '',
                        ghiChu: item.ghiChu || '',
                        ngayHenKham: item.ngayHenKham || '',
                        maTTDV: item.maTTDV || '',
                        maBS: item.maBS || '',
                        ngayCT: item.ngayCT || '',
                        maTheTam: item.maTheTam || '',
                        duPhong: item.duPhong || ''
                    });
                });
            }
            break;
    }
    return data;
}

// Hàm tạo cấu hình cột cho Tabulator
function getColumnsConfig(xmlType) {
    var columns = [];

    switch(xmlType) {
        case 'XML0':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true},
                { title: "Mã bệnh nhân", field: "maBN",headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên", field: "hoTen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số CCCD", field: "soCCCD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh", field: "ngaySinh", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                {
                    title: "Giới tính", field: "gioiTinh", headerFilter: true, headerVertical: false, editor: "list",
                    editorParams: {
                        values: Object.entries(gioi_tinh).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                    },
                    formatter: formatKeyValue(gioi_tinh),
                },
                { title: "Mã thẻ BHYT", field: "maTheBHYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã ĐKBD", field: "maDKBD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giá trị thẻ từ", field: "gtTheTu", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Giá trị thẻ đến", field: "gtTheDen", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate},
                {
                    title: "Mã đối tượng KCB", field: "maDoiTuongKCB", headerFilter: true, headerVertical: false,
                    editor: "list",
                    editorParams: {
                        // Sử dụng dữ liệu đã load sẵn
                        values: getCategoryValues("doituongkcb"),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    // Thêm formatter để hiển thị tên đầy đủ thay vì mã
                    formatter: function(cell) {
                        var value = cell.getValue();
                        var values = getCategoryValues("doituongkcb");
                        return values[value] || value;
                    }
                },
                { title: "Ngày vào", field: "ngayVao", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Ngày vào nội trú", field: "ngayVaoNoiTru", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Lý do vào nội trú", field: "ly_do_vnt", headerFilter: true, headerVertical: false, editor:"input"},
                {
                    title: "Mã lý do vào nội trú", field: "ma_ly_do_vnt", headerFilter: true, headerVertical: false, editor:"list",
                    editorParams: {
                        // Sử dụng dữ liệu đã load sẵn
                        values: Object.entries(ma_doi_tuong).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //
                    },
                    // Thêm formatter để hiển thị tên đầy đủ cả mã và tên
                    formatter: formatKeyValue(ma_doi_tuong),
                },
                { title: "Mã loại KCB", field: "maLoaiKCB", headerFilter: true, headerVertical: false, editor:"list",
                    editorParams: {
                        // Sử dụng dữ liệu đã load sẵn
                        values: Object.entries(ma_loai_kcb).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    // Thêm formatter để hiển thị tên đầy đủ cả mã và tên
                    formatter: formatKeyValue(ma_loai_kcb),
                },
                { title: "Mã CSKCB", field: "maCSKCB", headerFilter: true, headerVertical: false, editor:"input"  },
                { title: "Mã dịch vụ", field: "maDichVu", headerFilter: true, headerVertical: false, editor:"input"  },
                { title: "Tên dịch vụ", field: "tenDichVu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thuốc", field: "ma_thuoc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên thuốc", field: "tenThuoc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã vật tư", field: "maVatTu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên vật tư", field: "tenVatTu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày y lệnh", field: "ngayYL", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML1':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Mã bệnh nhân", field: "maBN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên", field: "hoTen", width: 180, headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số CCCD", field: "soCCCD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh", field: "ngaySinh", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                {
                    title: "Giới tính", field: "gioiTinh", headerFilter: true, headerVertical: false, editor: "list",
                    editorParams: { values: Object.entries(gioi_tinh).map(([key, label]) => ({ value: key,label: `${key} - ${label}`}))},
                    formatter: formatKeyValue(gioi_tinh),
                },
                { title: "Nhóm máu", field: "nhomMau", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã quốc tịch", field: "maQuocTich", headerFilter: true, headerVertical: false, editor:"input" },
                {
                    title: "Mã dân tộc", field: "maDanToc", headerFilter: true, headerVertical: false, editor:"list",
                    editorParams: {
                        // Sử dụng dữ liệu đã load sẵn
                        values: Object.entries(ma_dan_toc).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    // Thêm formatter để hiển thị tên đầy đủ cả mã và tên
                    formatter: formatKeyValue(ma_dan_toc),
                },
                { title: "Mã nghề nghiệp", field: "maNgheNghiep", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Địa chỉ", field: "diaChi", width: 250, headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã tỉnh cư trú", field: "maTinhCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã huyện cư trú", field: "maHuyenCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã xã cư trú", field: "maXaCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số điện thoại", field: "soDienThoai", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thẻ BHYT", field: "maThe", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã ĐKBD", field: "maDKBD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giá trị thẻ từ", field: "gtTheTu", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Giá trị thẻ đến", field: "gtTheDen", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày miễn CCT", field: "ngayMienCCT", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Lý do vào viện", field: "lyDoVV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Lý do vào nội trú", field: "lyDoVNT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã lý do vào nội trú", field: "maLyDoVNT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chẩn đoán vào", field: "chanDoanVao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chẩn đoán ra viện", field: "chanDoanRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh chính", field: "maBenhChinh", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh kèm theo", field: "maBenhKT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh YHCT", field: "maBenhmaBenhYHCTKem2", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã PTTT QT", field: "maPTTTQT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã đối tượng KCB", field: "maDoiTuongKCB", headerFilter: true, headerVertical: false, editor: "input" },
                { title: "Mã nơi đi", field: "maNoiDi", headerFilter: true, headerVertical: false, editor: "input" },
                { title: "Mã nơi đến", field: "maNoiDen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã tai nạn", field: "maTaiNan", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày vào", field: "ngayVao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giấy chuyển tuyến", field: "giayChuyenTuyen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày ĐT", field: "soNgayDtri", headerFilter: true, headerVertical: false, editor: "input"},
                { title: "PP ĐT", field: "ppDieuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "KQ ĐT", field: "ketQuaDtri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã loại ra viện", field: "maLoaiRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày thanh toán", field: "ngayTToan", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tổng chi thuốc", field: "tThuoc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tổng chi VTYT", field: "tVTYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tổng chi BV", field: "tTongChiBV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tổng chi BH", field: "tTongChiBH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BNTT", field: "tBNTT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BNCCT", field: "tBNCCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BHTT", field: "tBHTT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn khác", field: "tNguonKhac", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BHTT GDV", field: "tBHTTGDV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Năm QT", field: "namQT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tháng QT", field: "thangQT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã loại KCB", field: "maLoaiKCB", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã khoa", field: "maKhoa", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã CSKCB", field: "maCSKCB", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã khu vực", field: "maKhuVuc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Cân nặng", field: "canNang", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Cân nặng con", field: "canNangCon", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "5 năm LT", field: "namNamLienTuc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày tái khám", field: "ngayTaiKham", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã HSBA", field: "maHSBA", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã TTDV", field: "maTTDV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày tạo", field: "ngayTao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày chỉnh sửa", field: "ngayChinhSua", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "TT gửi BHXH", field: "trangThaiGuiBHXH", headerFilter: true, headerVertical: false, editor:"input" },
            ]);
            break;

        case 'XML2':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "STT", field: "stt", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thuốc", field: "maThuoc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã PP chế biến", field: "maPPCheBien", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã CSKCB thuốc", field: "maCSKCBThuoc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã nhóm", field: "maNhom", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên thuốc", field: "tenThuoc", width: 200, headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn vị tính", field: "donViTinh", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Hàm lượng", field: "hamLuong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đường dùng", field: "duongDung", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dạng bào chế", field: "dangBaoChe", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Liều dùng", field: "lieuDung", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Cách dùng", field: "cachDung", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số đăng ký", field: "soDangKy", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "TT thầu", field: "ttThau", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phạm vi", field: "phamVi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tỷ lệ TT BH", field: "tyLeTTBH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số lượng", field: "soLuong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn giá", field: "donGia", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Thành tiền BV", field: "thanhTienBV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Thành tiền BH", field: "thanhTienBH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn NSNN", field: "tNguonKhacNSNN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn VTNN", field: "tNguonKhacVTNN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn VTTN", field: "tNguonKhacVTTN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn còn lại", field: "tNguonKhacCL", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn khác", field: "tNguonKhac", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mức hưởng", field: "mucHuong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BN thanh toán", field: "tBNTT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BN cùng chi trả", field: "tBNCCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BH thanh toán", field: "tBHTT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã khoa", field: "maKhoa", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bác sĩ", field: "maBacSi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã dịch vụ", field: "maDichVu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày y lệnh", field: "ngayYL", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Ngày TH y lệnh", field: "ngayTHYL", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Mã PTTT", field: "maPTTT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn chi trả", field: "nguonCTra", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Vết thương TP", field: "vetThuongTP", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML3':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "STT", field: "stt", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã vật tư", field: "maVatTu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã nhóm", field: "maNhom", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên vật tư", field: "tenVatTu", width: 200, headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn vị tính", field: "donViTinh", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phạm vi", field: "phamVi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số lượng", field: "soLuong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn giá BV", field: "donGiaBV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn giá BH", field: "donGiaBH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tỷ lệ BH", field: "tyLeBH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Thành tiền BV", field: "thanhTienBV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Thành tiền BH", field: "thanhTienBH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã khoa", field: "maKhoa", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên khoa", field: "tenKhoa", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bác sĩ", field: "maBacSi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên bác sĩ", field: "tenBacSi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày y lệnh", field: "ngayYLenh", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Ngày dùng", field: "ngayDung", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML4':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "STT", field: "stt", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã dịch vụ", field: "maDichVu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã chỉ số", field: "maChiSo", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên chỉ số", field: "tenChiSo", width: 200, headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giá trị", field: "giaTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã máy", field: "maMay", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mô tả", field: "moTa", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Kết luận", field: "ketLuan", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML5':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "STT", field: "stt", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày khám", field: "ngayKham", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Mã bác sĩ", field: "maBacSi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên bác sĩ", field: "tenBacSi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã khoa", field: "maKhoa", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên khoa", field: "tenKhoa", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Diễn biến", field: "dienBien", width: 300, headerFilter: true, headerVertical: false, editor:"textarea" },
                { title: "Hội chẩn", field: "hoiChan", width: 300, headerFilter: true, headerVertical: false, editor:"textarea" },
                { title: "Phẫu thuật", field: "phauThuat", width: 300, headerFilter: true, headerVertical: false, editor:"textarea" },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML6':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Mã thẻ BHYT", field: "maTheBHYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số CCCD", field: "soCCCD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh", field: "ngaySinh", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã tỉnh cư trú", field: "maTinhCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã huyện cư trú", field: "maHuyenCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã xã cư trú", field: "maXaCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày khẳng định HIV", field: "ngayKDHIV", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Nơi lấy mẫu XN", field: "noiLayMauXN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nơi XN khẳng định", field: "noiXNKD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nơi bắt đầu điều trị ARV", field: "noiBDDTARV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã phác đồ điều trị ban đầu", field: "maPhaDoDieuTriBD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Loại điều trị lao", field: "loaiDtriLao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phác đồ điều trị lao", field: "phacDoDtriLao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Kết quả điều trị lao", field: "kqDTLao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML7':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Số lưu trữ", field: "soLuuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã y tế", field: "maYTe", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã khoa ra viện", field: "maKhoaRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày vào", field: "ngayVao", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày ra", field: "ngayRa", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã định chỉ thai", field: "maDinhChiThai", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chẩn đoán ra viện", field: "chanDoanRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phương pháp điều trị", field: "ppDieuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tuổi thai", field: "tuoiThai", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguyên nhân định chỉ", field: "nguyenNhanDinhChi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã cha", field: "maCha", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã mẹ", field: "maMe", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên cha", field: "hoTenCha", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên mẹ", field: "hoTenMe", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML8':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Họ tên cha", field: "hoTenCha", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên mẹ", field: "hoTenMe", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Người giám hộ", field: "nguoiGiamHo", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày vào", field: "ngayVao", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày ra", field: "ngayRa", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Chẩn đoán vào", field: "chanDoanVao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chẩn đoán ra viện", field: "chanDoanRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Quá trình bệnh lý", field: "qtBenhLy", width: 300, headerFilter: true, headerVertical: false, editor:"textarea" },
                { title: "Tóm tắt kết quả", field: "tomTatKQ", width: 300, headerFilter: true, headerVertical: false, editor:"textarea" },
                { title: "Ngày sinh con", field: "ngaySinhCon", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày con chết", field: "ngayConChet", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Số con chết", field: "soConChet", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phương pháp điều trị", field: "ppDieuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Kết quả điều trị", field: "ketQuaDtri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML9':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Họ tên người nuôi dưỡng", field: "hotenNND", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh NND", field: "ngaySinhNND", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Số CCCD NND", field: "soCCCDNND", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nơi cư trú NND", field: "noiCuTruNND", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã tỉnh cư trú", field: "maTinhCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã huyện cư trú", field: "maHuyenCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã xã cư trú", field: "maXaCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên con", field: "hoTenCon", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giới tính con", field: "gioiTinhCon", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số con", field: "soCon", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Lần sinh", field: "lanSinh", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Cân nặng con", field: "canNangCon", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh con", field: "ngaySinhCon", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Nơi sinh con", field: "noiSinhCon", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tình trạng con", field: "tinhTrangCon", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Sinh con phẫu thuật", field: "sinhConPhauThuat", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Người đỡ đẻ", field: "nguoiDoDe", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML10':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Số seri", field: "soSeri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số chứng từ", field: "soCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số ngày", field: "soNgay", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn vị", field: "donVi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chẩn đoán ra viện", field: "chanDoanRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Từ ngày", field: "tuNgay", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Đến ngày", field: "denNgay", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã bác sĩ", field: "maBS", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên bác sĩ", field: "tenBS", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày chứng từ", field: "ngayCT", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML11':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Số chứng từ", field: "soCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số seri", field: "soSeri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số KCB", field: "soKCB", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn vị", field: "donVi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã BHXH", field: "maBHXH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thẻ BHYT", field: "maTheBHYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chẩn đoán ra viện", field: "chanDoanRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phương pháp điều trị", field: "ppDieuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã định chỉ thai", field: "maDinhChiThai", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tuổi thai", field: "tuoiThai", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguyên nhân định chỉ", field: "nguyenNhanDinhChi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số ngày nghỉ", field: "soNgayNghi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Từ ngày", field: "tuNgay", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Đến ngày", field: "denNgay", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML12':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Người chủ trì", field: "nguoiChuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chức vụ", field: "chucVu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày họp", field: "ngayHop", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Họ tên", field: "hoTen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh", field: "ngaySinh", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Số CCCD", field: "soCCCD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Địa chỉ", field: "diaChi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã tỉnh cư trú", field: "maTinhCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã huyện cư trú", field: "maHuyenCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã xã cư trú", field: "maXaCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã BHXH", field: "maBHXH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thẻ BHYT", field: "maTheBHYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tỷ lệ TTCT cũ", field: "tyLeTTCTCu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tỷ lệ TTCT mới", field: "tyLeTTCTMoi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tổng tỷ lệ TTCT", field: "tongTyLeTTCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML13':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Số hồ sơ", field: "soHoSo", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số chuyển tuyến", field: "soChuyenTuyen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giấy chuyển tuyến", field: "giayChuyenTuyen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã CSKCB", field: "maCSKCB", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã nơi đi", field: "maNoiDi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã nơi đến", field: "maNoiDen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên", field: "hoTen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh", field: "ngaySinh", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                {
                    title: "Giới tính",
                    field: "gioiTinh",
                    headerFilter: true,
                    headerVertical: false,
                    editor: "list",
                    editorParams: {
                        values: Object.entries(gioi_tinh).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                    },
                    formatter: formatKeyValue(gioi_tinh),
                },
                { title: "Mã thẻ BHYT", field: "maTheBHYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giá trị thẻ đến", field: "gtTheDen", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Chẩn đoán ra viện", field: "chanDoanRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Quá trình bệnh lý", field: "qtBenhLy", width: 300, headerFilter: true, headerVertical: false, editor:"textarea" },
                { title: "Hướng điều trị", field: "huongDieuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML14':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Số giấy hẹn KL", field: "soGiayHenKL", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã CSKCB", field: "maCSKCB", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên", field: "hoTen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh", field: "ngaySinh", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                {
                    title: "Giới tính",
                    field: "gioiTinh",
                    headerFilter: true,
                    headerVertical: false,
                    editor: "list",
                    editorParams: {
                        values: Object.entries(gioi_tinh).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                    },
                    formatter: formatKeyValue(gioi_tinh),
                },
                { title: "Địa chỉ", field: "diaChi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thẻ BHYT", field: "maTheBHYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giá trị thẻ đến", field: "gtTheDen", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày vào", field: "ngayVao", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày ra", field: "ngayRa", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày hẹn khám lại", field: "ngayHenKL", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Chẩn đoán ra viện", field: "chanDoanRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh chính", field: "maBenhChinh", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh kèm theo", field: "maBenhKT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML15':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Mã bệnh nhân", field: "maBN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên", field: "hoTen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số CCCD", field: "soCCCD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phân loại lao vị trí", field: "phanLoaiLaoViTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phân loại lao tình trạng", field: "phanLoaiLaoTS", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phân loại lao HIV", field: "phanLoaiLaoHIV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Loại điều trị lao", field: "LoaiDTriLao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phác đồ điều trị lao", field: "phacDoDTriLao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày bắt đầu điều trị lao", field: "ngayBDDTriLao", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày kết thúc điều trị lao", field: "ngayKTDTriLao", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Kết quả điều trị lao", field: "ketQuaDTriLao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;
    }

    // Cột thao tác (cột cuối cùng)
    columns.push({
        title: "Thao tác",
        formatter: function(cell, formatterParams, onRendered) {
            var row = cell.getRow();
            var data = row.getData();
            var id = data.id;
            var maLK = data.maLK;
            var type = xmlType;

            var editButton = '';
            if (maLK) {
                editButton = '<a href="/xml4750/edit/' + type + '/' + maLK + '/" class="btn btn-sm btn-primary mr-1" title="Sửa"><i class="fas fa-edit"></i></a>';
            } else {
                editButton = '<button type="button" class="btn btn-sm btn-primary mr-1 disabled" title="Sửa"><i class="fas fa-edit"></i></button>';
            }

            var deleteButton = '<button type="button" class="btn btn-sm btn-danger mr-1 delete-xml" data-id="' + id + '" data-type="' + type + '" title="Xóa"><i class="fas fa-trash"></i></button>';
            var exportButton = '<button type="button" class="btn btn-sm btn-success export-xml" data-id="' + id + '" data-type="' + type + '" title="Xuất XML"><i class="fas fa-file-export"></i></button>';

            return '<div class="btn-group">' + editButton + deleteButton + exportButton + '</div>';
        },
        width: 130,
        headerSort: false,
        hozAlign: "center",
        headerHozAlign: "center",
        headerVertical: false,
        resizable: false,
        cssClass: "action-column",
        frozen:true
    });

    return columns;
}

// Hàm thiết lập Tabulator
function setupTables(djangoData) {
    // Xử lý tab đang hiển thị
    var $activeTab = $('.tab-pane.active');
    if ($activeTab.length > 0) {
        var tabId = $activeTab.attr('id');
        var xmlType = tabId.toUpperCase();

        // Khởi tạo Tabulator cho tab đang hiển thị
        if ($activeTab.find('.xml-tabulator').length > 0) {
            var tableElement = document.getElementById(tabId + '-table');

            // Kiểm tra xem Tabulator đã được khởi tạo chưa
            if (!tableElement._tabulator) {
                var columns = getColumnsConfig(xmlType);

                // Debug: Log thông tin phân trang
                console.log("Pagination info for " + xmlType + ":", djangoData[xmlType.toLowerCase() + '_pagination']);

                // Khởi tạo Tabulator mới
                var table = new Tabulator(tableElement, {
                    columns: columns,
                    layout: "fitDataFill",
                    pagination:true, //enable pagination
                    paginationMode: "remote", // Sử dụng phân trang từ xa
                    paginationSize: 20,
                    paginationSizeSelector:[10, 20, 50, 100],
                    paginationInitialPage: (djangoData[xmlType.toLowerCase() + '_pagination'] && djangoData[xmlType.toLowerCase() + '_pagination'].current_page) ? djangoData[xmlType.toLowerCase() + '_pagination'].current_page : 1, //optional parameter to set the initial page to load
                    ajaxURL: "/xml4750/api/data/", // Sử dụng API endpoint mới
                    ajaxParams: {
                        xml_type: xmlType
                    },
                    ajaxConfig: "GET",
                    ajaxResponse: function(url, params, response) {
                        // Xử lý dữ liệu trả về từ API
                        return response;
                    },
                    scrollX: true,
                    autoResize: true,
                    movableColumns: true,
                    selectable: true,
                    height: "calc(100vh - 350px)",
                    placeholder: "Không có dữ liệu",
                    tooltips: true,
                    headerFilterLiveFilterDelay: 300,
                    resizableColumnFit: true,
                    fixedHeader: true, // Fix the header
                    cellEdited:function(cell){
                        var rowData = cell.getRow().getData();
                        console.log("Updated " + xmlType + " row:", rowData);
                        // Gửi AJAX cập nhật lên server
                        saveEditedCell(cell);
                    },
                    langs: {
                        "vi-vn": {
                            "columns": {
                                "name": "Tên",
                                "progress": "Tiến độ",
                                "gender": "Giới tính",
                                "rating": "Đánh giá",
                                "col": "Cột"
                            },
                            "ajax": {
                                "loading": "Đang tải",
                                "error": "Lỗi"
                            },
                            "pagination": {
                                "page_size": "Kích thước trang",
                                "page_title": "Hiển thị trang",
                                "first": "Đầu",
                                "first_title": "Trang đầu",
                                "last": "Cuối",
                                "last_title": "Trang cuối",
                                "prev": "Trước",
                                "prev_title": "Trang trước",
                                "next": "Tiếp",
                                "next_title": "Trang tiếp theo"
                            },
                            "headerFilters": {
                                "default": "Tìm kiếm...",
                            }
                        }
                    }
                });
            }
        }
    }
}
