import xml.etree.ElementTree as ET
import base64
from datetime import datetime
from io import BytesIO
from .models import (
    XML0Model, XML1Model, XML2Model, XML3Model, XML4Model, XML5Model,
    XML6Model, XML7Model, XML8Model, XML9Model, XML10Model, XML11Model,
    XML12Model, XML13Model, XML14Model, XML15Model
)

# Dictionary to map XML type to model
XML_MODEL_MAP = {
    'XML0': XML0Model,
    'XML1': XML1Model,
    'XML2': XML2Model,
    'XML3': XML3Model,
    'XML4': XML4Model,
    'XML5': XML5Model,
    'XML6': XML6Model,
    'XML7': XML7Model,
    'XML8': XML8Model,
    'XML9': XML9Model,
    'XML10': XML10Model,
    'XML11': XML11Model,
    'XML12': XML12Model,
    'XML13': XML13Model,
    'XML14': XML14Model,
    'XML15': XML15Model,
}

# Dictionary to map XML type to root tag
XML_ROOT_TAG_MAP = {
    'XML0': 'CHI_TIEU_TRANG_THAI_KCB/DSACH_TRANG_THAI_KCB/TRANG_THAI_KCB',
    'XML1': 'TONG_HOP',
    'XML2': 'CHITIEU_CHITIET_THUOC/DSACH_CHI_TIET_THUOC/CHI_TIET_THUOC',
    'XML3': 'CHITIEU_CHITIET_DVKT_VTYT/DSACH_CHI_TIET_DVKT/CHI_TIET_DVKT',
    'XML4': 'CHITIEU_CHITIET_DICHVUCANLAMSANG/DSACH_CHI_TIET_CLS/CHI_TIET_CLS',
    'XML5': 'CHITIEU_CHITIET_DIENBIENLAMSANG/DSACH_CHI_TIET_DIEN_BIEN_BENH/CHI_TIET_DIEN_BIEN_BENH',
    'XML6': 'CHI_TIEU_HO_SO_BENH_AN_CHAM_SOC_VA_DIEU_TRI_HIV_AIDS/DSACH_HO_SO_BENH_AN_CHAM_SOC_VA_DIEU_TRI_HIV_AIDS/HO_SO_BENH_AN_CHAM_SOC_VA_DIEU_TRI_HIV_AIDS',
    'XML7': 'CHI_TIEU_DU_LIEU_GIAY_RA_VIEN',
    'XML8': 'CHI_TIEU_DU_LIEU_TOM_TAT_HO_SO_BENH_AN',
    'XML9': 'CHI_TIEU_DU_LIEU_GIAY_CHUNG_SINH/DSACH_GIAYCHUNGSINH/DU_LIEU_GIAY_CHUNG_SINH',
    'XML10': 'CHI_TIEU_DU_LIEU_GIAY_NGHI_DUONG_THAI',
    'XML11': 'CHI_TIEU_DU_LIEU_GIAY_CHUNG_NHAN_NGHI_VIEC_HUONG_BAO_HIEM_XA_HOI',
    'XML12': 'CHI_TIEU_DU_LIEU_GIAM_DINH_Y_KHOA',
    'XML13': 'CHI_TIEU_GIAYCHUYENTUYEN',
    'XML14': 'CHI_TIEU_GIAYHEN_KHAMLAI',
    'XML15': 'CHI_TIEU_DIEUTRI_BENHLAO/DSACH_CHITIET_DIEUTRI_BENHLAO/CHITIET_DIEUTRI_BENHLAO',
}

def camel_to_snake(name):
    """
    Convert camelCase to snake_case
    Example: maLK -> ma_lk
    """
    import re
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

def snake_to_camel(name):
    """
    Convert snake_case to camelCase
    Example: ma_lk -> maLK
    """
    components = name.split('_')
    return components[0] + ''.join(x.title() for x in components[1:])

def xml_tag_to_field_name(tag):
    """
    Convert XML tag to field name
    Example: MA_LK -> maLK
    Example: MA_THE_BHYT -> maTheBHYT
    Example: LY_DO_VNT -> ly_do_vnt (special case for snake_case fields)
    """
    # Special case mapping for fields that use snake_case in the model
    special_cases = {
        'LY_DO_VNT': 'lyDoVNT',
        'MA_LY_DO_VNT': 'maLyDoVNT',
        'MA_THUOC': 'maThuoc',
        'DU_PHONG': 'duPhong',
        'MA_LK': 'maLK',
        'MA_BN': 'maBN',
        'HO_TEN': 'hoTen',
        'SO_CCCD': 'soCCCD',
        'NGAY_SINH': 'ngaySinh',
        'GIOI_TINH': 'gioiTinh',
        'NHOM_MAU': 'nhomMau',
        'MA_QUOCTICH': 'maQuocTich',
        'MA_DANTOC': 'maDanToc',
        'MA_NGHE_NGHIEP': 'maNgheNghiep',
        'DIA_CHI': 'diaChi',
        'MA_TINH_CU_TRU': 'maTinhCuTru',
        'MA_HUYEN_CU_TRU': 'maHuyenCuTru',
        'MA_XA_CU_TRU': 'maXaCuTru',
        'DIEN_THOAI': 'dienThoai',
        'MA_THE_BHYT': 'maTheBHYT',
        'MA_DKBD': 'maDKBD',
        'GT_THE_TU': 'gtTheTu',
        'GT_THE_DEN': 'gtTheDen',
        'NGAY_MIEN_CCT': 'ngayMienCCT',
        'LY_DO_VV': 'lyDoVV',
        'CHAN_DOAN_VAO': 'chanDoanVao',
        'CHAN_DOAN_RV': 'chanDoanRV',
        'MA_BENH_CHINH': 'maBenhChinh',
        'MA_BENH_KT': 'maBenhKT',
        'MA_BENH_YHCT': 'maBenhYHCT',
        'MA_PTTT_QT': 'maPTTTQT',
        'MA_DOI_TUONG_KCB': 'maDoiTuongKCB',
        'MA_NOI_DI': 'maNoiDi',
        'MA_NOI_DEN': 'maNoiDen',
        'MA_TAI_NAN': 'maTaiNan',
        'NGAY_VAO': 'ngayVao',
        'NGAY_VAO_NOI_TRU': 'ngayVaoNoiTru',
        'NGAY_RA': 'ngayRa',
        'GIAY_CHUYEN_TUYEN': 'giayChuyenTuyen',
        'SO_NGAY_DTRI': 'soNgayDtri',
        'PP_DIEU_TRI': 'ppDieuTri',
        'KET_QUA_DTRI': 'ketQuaDtri',
        'MA_LOAI_RV': 'maLoaiRV',
        'GHI_CHU': 'ghiChu',
        'NGAY_TTOAN': 'ngayTToan',
        'T_THUOC': 'tThuoc',
        'T_VTYT': 'tVTYT',
        'T_TONG_CHI_BV': 'tTongChiBV',
        'T_TONG_CHI_BH': 'tTongChiBH',
        'T_BNTT': 'tBNTT',
        'T_BNCCT': 'tBNCCT',
        'T_BHTT': 'tBHTT',
        'T_NGUON_KHAC': 'tNguonKhac',
        'T_BHTT_GDV': 'tBHTTGDV',
        'NAM_QT': 'namQT',
        'THANG_QT': 'thangQT',
        'MA_LOAI_KCB': 'maLoaiKCB',
        'MA_KHOA': 'maKhoa',
        'MA_CSKCB': 'maCSKCB',
        'MA_KHU_VUC': 'maKhuVuc',
        'CAN_NANG': 'canNang',
        'CAN_NANG_CON': 'canNangCon',
        'NAM_NAM_LIEN_TUC': 'namNamLienTuc',
        'NGAY_TAI_KHAM': 'ngayTaiKham',
        'MA_HSBA': 'maHSBA',
        'MA_TTDV': 'maTTDV',
        'MA_DICH_VU': 'maDichVu',
        'TEN_DICH_VU': 'tenDichVu',
        'TEN_THUOC': 'tenThuoc',
        'MA_VAT_TU': 'maVatTu',
        'TEN_VAT_TU': 'tenVatTu',
        'NGAY_YL': 'ngayYL',
        # Thêm các trường mới cho XML2
        'MA_PP_CHEBIEN': 'maPPCheBien',
        'MA_CSKCB_THUOC': 'maCSKCBThuoc',
        'MA_NHOM': 'maNhom',
        'DON_VI_TINH': 'donViTinh',
        'HAM_LUONG': 'hamLuong',
        'DUONG_DUNG': 'duongDung',
        'DANG_BAO_CHE': 'dangBaoChe',
        'LIEU_DUNG': 'lieuDung',
        'CACH_DUNG': 'cachDung',
        'SO_DANG_KY': 'soDangKy',
        'TT_THAU': 'ttThau',
        'PHAM_VI': 'phamVi',
        'TYLE_TT_BH': 'tyLeTTBH',
        'SO_LUONG': 'soLuong',
        'DON_GIA': 'donGia',
        'THANH_TIEN_BV': 'thanhTienBV',
        'THANH_TIEN_BH': 'thanhTienBH',
        'T_NGUONKHAC_NSNN': 'tNguonKhacNSNN',
        'T_NGUONKHAC_VTNN': 'tNguonKhacVTNN',
        'T_NGUONKHAC_VTTN': 'tNguonKhacVTTN',
        'T_NGUONKHAC_CL': 'tNguonKhacCL',
        'MUC_HUONG': 'mucHuong',
        'MA_BAC_SI': 'maBacSi',
        'NGAY_TH_YL': 'ngayTHYL',
        'MA_PTTT': 'maPTTT',
        'NGUON_CTRA': 'nguonCTra',
        'VET_THUONG_TP': 'vetThuongTP',
        'DU_PHONG': 'duPhong'
    }

    if tag in special_cases:
        return special_cases[tag]

    components = tag.lower().split('_')
    return components[0] + ''.join(x.title() for x in components[1:])

def field_name_to_xml_tag(field_name):
    """
    Convert field name to XML tag
    Example: maLK -> MA_LK
    Example: maTheBHYT -> MA_THE_BHYT
    Example: ly_do_vnt -> LY_DO_VNT (special case for snake_case fields)
    """
    # Special case mapping for fields that use snake_case in the model
    special_cases = {
        'lyDoVNT': 'LY_DO_VNT',
        'maLyDoVNT': 'MA_LY_DO_VNT',
        'maThuoc': 'MA_THUOC',
        'duPhong': 'DU_PHONG',
        'maLK': 'MA_LK',
        'maBN': 'MA_BN',
        'hoTen': 'HO_TEN',
        'soCCCD': 'SO_CCCD',
        'ngaySinh': 'NGAY_SINH',
        'gioiTinh': 'GIOI_TINH',
        'nhomMau': 'NHOM_MAU',
        'maQuocTich': 'MA_QUOCTICH',
        'maDanToc': 'MA_DANTOC',
        'maNgheNghiep': 'MA_NGHE_NGHIEP',
        'diaChi': 'DIA_CHI',
        'maTinhCuTru': 'MA_TINH_CU_TRU',
        'maHuyenCuTru': 'MA_HUYEN_CU_TRU',
        'maXaCuTru': 'MA_XA_CU_TRU',
        'dienThoai': 'DIEN_THOAI',
        'maTheBHYT': 'MA_THE_BHYT',
        'maDKBD': 'MA_DKBD',
        'gtTheTu': 'GT_THE_TU',
        'gtTheDen': 'GT_THE_DEN',
        'ngayMienCCT': 'NGAY_MIEN_CCT',
        'lyDoVV': 'LY_DO_VV',
        'chanDoanVao': 'CHAN_DOAN_VAO',
        'chanDoanRV': 'CHAN_DOAN_RV',
        'maBenhChinh': 'MA_BENH_CHINH',
        'maBenhKT': 'MA_BENH_KT',
        'maBenhYHCT': 'MA_BENH_YHCT',
        'maPTTTQT': 'MA_PTTT_QT',
        'maDoiTuongKCB': 'MA_DOI_TUONG_KCB',
        'maNoiDi': 'MA_NOI_DI',
        'maNoiDen': 'MA_NOI_DEN',
        'maTaiNan': 'MA_TAI_NAN',
        'ngayVao': 'NGAY_VAO',
        'ngayVaoNoiTru': 'NGAY_VAO_NOI_TRU',
        'ngayRa': 'NGAY_RA',
        'giayChuyenTuyen': 'GIAY_CHUYEN_TUYEN',
        'soNgayDtri': 'SO_NGAY_DTRI',
        'ppDieuTri': 'PP_DIEU_TRI',
        'ketQuaDtri': 'KET_QUA_DTRI',
        'maLoaiRV': 'MA_LOAI_RV',
        'ghiChu': 'GHI_CHU',
        'ngayTToan': 'NGAY_TTOAN',
        'tThuoc': 'T_THUOC',
        'tVTYT': 'T_VTYT',
        'tTongChiBV': 'T_TONG_CHI_BV',
        'tTongChiBH': 'T_TONG_CHI_BH',
        'tBNTT': 'T_BNTT',
        'tBNCCT': 'T_BNCCT',
        'tBHTT': 'T_BHTT',
        'tNguonKhac': 'T_NGUON_KHAC',
        'tBHTTGDV': 'T_BHTT_GDV',
        'namQT': 'NAM_QT',
        'thangQT': 'THANG_QT',
        'maLoaiKCB': 'MA_LOAI_KCB',
        'maKhoa': 'MA_KHOA',
        'maCSKCB': 'MA_CSKCB',
        'maKhuVuc': 'MA_KHU_VUC',
        'canNang': 'CAN_NANG',
        'canNangCon': 'CAN_NANG_CON',
        'namNamLienTuc': 'NAM_NAM_LIEN_TUC',
        'ngayTaiKham': 'NGAY_TAI_KHAM',
        'maHSBA': 'MA_HSBA',
        'maTTDV': 'MA_TTDV',
        'maDichVu': 'MA_DICH_VU',
        'tenDichVu': 'TEN_DICH_VU',
        'tenThuoc': 'TEN_THUOC',
        'maVatTu': 'MA_VAT_TU',
        'tenVatTu': 'TEN_VAT_TU',
        'ngayYL': 'NGAY_YL',
        # Thêm các trường mới cho XML2
        'maPPCheBien': 'MA_PP_CHEBIEN',
        'maCSKCBThuoc': 'MA_CSKCB_THUOC',
        'maNhom': 'MA_NHOM',
        'donViTinh': 'DON_VI_TINH',
        'hamLuong': 'HAM_LUONG',
        'duongDung': 'DUONG_DUNG',
        'dangBaoChe': 'DANG_BAO_CHE',
        'lieuDung': 'LIEU_DUNG',
        'cachDung': 'CACH_DUNG',
        'soDangKy': 'SO_DANG_KY',
        'ttThau': 'TT_THAU',
        'phamVi': 'PHAM_VI',
        'tyLeTTBH': 'TYLE_TT_BH',
        'soLuong': 'SO_LUONG',
        'donGia': 'DON_GIA',
        'thanhTienBV': 'THANH_TIEN_BV',
        'thanhTienBH': 'THANH_TIEN_BH',
        'tNguonKhacNSNN': 'T_NGUONKHAC_NSNN',
        'tNguonKhacVTNN': 'T_NGUONKHAC_VTNN',
        'tNguonKhacVTTN': 'T_NGUONKHAC_VTTN',
        'tNguonKhacCL': 'T_NGUONKHAC_CL',
        'mucHuong': 'MUC_HUONG',
        'maBacSi': 'MA_BAC_SI',
        'ngayTHYL': 'NGAY_TH_YL',
        'maPTTT': 'MA_PTTT',
        'nguonCTra': 'NGUON_CTRA',
        'vetThuongTP': 'VET_THUONG_TP',
        'duPhong': 'DU_PHONG'
    }

    if field_name in special_cases:
        return special_cases[field_name]

    import re
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', field_name)
    s2 = re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1)
    return s2.upper()

def parse_xml_to_model(xml_type, xml_content):
    """
    Parse XML content and convert to Django model
    """
    model_class = XML_MODEL_MAP.get(xml_type)
    if not model_class:
        raise ValueError(f"Invalid XML type: {xml_type}")

    # Fix common XML declaration issues
    if '<?xml' in xml_content:
        # Fix invalid XML declaration
        if '<?xml version="encoding=' in xml_content:
            xml_content = xml_content.replace('<?xml version="encoding=', '<?xml version="1.0" encoding=')

        # Fix other potential XML declaration issues
        if not ('<?xml version=' in xml_content and 'encoding=' in xml_content):
            xml_content = xml_content.replace('<?xml', '<?xml version="1.0" encoding="utf-8"')

    # Parse XML
    try:
        root = ET.fromstring(xml_content)
    except ET.ParseError as e:
        # Try to remove the XML declaration and parse again
        if '<?xml' in xml_content:
            try:
                # Remove the XML declaration
                xml_content_no_decl = xml_content[xml_content.find('?>')+2:].strip()
                root = ET.fromstring(xml_content_no_decl)
            except ET.ParseError:
                raise ValueError(f"Invalid XML format: {str(e)}")
        else:
            raise ValueError(f"Invalid XML format: {str(e)}")

    # Get root tag path
    root_tag_path = XML_ROOT_TAG_MAP.get(xml_type)
    if not root_tag_path:
        raise ValueError(f"Root tag path not defined for XML type: {xml_type}")

    # Split path and navigate to the element
    path_parts = root_tag_path.split('/')
    element = root

    # Special handling for XML0 which has a deeper structure
    if xml_type == 'XML0':
        # For XML0, we need to handle the structure:
        # CHI_TIEU_TRANG_THAI_KCB/DSACH_TRANG_THAI_KCB/TRANG_THAI_KCB
        try:
            # First, try to find TRANG_THAI_KCB directly (most specific element)
            trang_thai_elements = root.findall('.//TRANG_THAI_KCB')
            if trang_thai_elements:
                # Nếu có nhiều phần tử TRANG_THAI_KCB, chỉ lấy phần tử đầu tiên
                if len(trang_thai_elements) > 1:
                    print(f"Warning: Found {len(trang_thai_elements)} TRANG_THAI_KCB elements, using the first one")
                element = trang_thai_elements[0]
            else:
                # If not found, try to navigate through the path
                for i, part in enumerate(path_parts):
                    # Strip any namespace from the tag
                    part_tag = part.split('}')[-1] if '}' in part else part

                    # For the last part (TRANG_THAI_KCB), we want the first occurrence
                    if i == len(path_parts) - 1:
                        # Find all elements with this tag
                        elements = element.findall(f'.//{part_tag}')
                        if elements:
                            element = elements[0]
                        else:
                            # Try direct child
                            child = element.find(part_tag)
                            if child is not None:
                                element = child
                            else:
                                raise ValueError(f"Element {part} not found in XML")
                    else:
                        # For other parts, try direct child first
                        child = element.find(part_tag)
                        if child is not None:
                            element = child
                        else:
                            # Try with XPath
                            elements = element.findall(f'.//{part_tag}')
                            if elements:
                                element = elements[0]
                            else:
                                raise ValueError(f"Element {part} not found in XML")
        except Exception as e:
            raise ValueError(f"Error navigating XML structure: {str(e)}")
    elif xml_type == 'XML1':
        # For XML1, we need to handle the structure:
        # TONG_HOP
        try:
            # First, check if the root tag is TONG_HOP
            root_tag = root.tag.split('}')[-1] if '}' in root.tag else root.tag
            if root_tag == 'TONG_HOP':
                # Use the root element directly
                element = root
            else:
                # Try to find THONG_TIN_HANH_CHINH directly (most specific element)
                hanh_chinh_elements = root.findall('.//THONG_TIN_HANH_CHINH')
                if hanh_chinh_elements:
                    # Nếu có nhiều phần tử THONG_TIN_HANH_CHINH, chỉ lấy phần tử đầu tiên
                    if len(hanh_chinh_elements) > 1:
                        print(f"Warning: Found {len(hanh_chinh_elements)} THONG_TIN_HANH_CHINH elements, using the first one")
                    element = hanh_chinh_elements[0]
                else:
                    # If not found, try to navigate through the path
                    for i, part in enumerate(path_parts):
                        # Strip any namespace from the tag
                        part_tag = part.split('}')[-1] if '}' in part else part

                        # For the last part, we want the first occurrence
                        if i == len(path_parts) - 1:
                            # Find all elements with this tag
                            elements = element.findall(f'.//{part_tag}')
                            if elements:
                                element = elements[0]
                            else:
                                # Try direct child
                                child = element.find(part_tag)
                                if child is not None:
                                    element = child
                                else:
                                    raise ValueError(f"Element {part} not found in XML")
                        else:
                            # For other parts, try direct child first
                            child = element.find(part_tag)
                            if child is not None:
                                element = child
                            else:
                                # Try with XPath
                                elements = element.findall(f'.//{part_tag}')
                                if elements:
                                    element = elements[0]
                                else:
                                    # Nếu không tìm thấy DSACH_THONG_TIN_HANH_CHINH, thử tìm trực tiếp THONG_TIN_HANH_CHINH
                                    if part_tag == 'DSACH_THONG_TIN_HANH_CHINH' and i == 0:
                                        direct_elements = root.findall('.//THONG_TIN_HANH_CHINH')
                                        if direct_elements:
                                            # Bỏ qua phần tử DSACH_THONG_TIN_HANH_CHINH và sử dụng trực tiếp THONG_TIN_HANH_CHINH
                                            element = direct_elements[0]
                                            # Bỏ qua các phần tử còn lại trong path vì đã tìm thấy phần tử cuối cùng
                                            return element
                                        else:
                                            raise ValueError(f"Element {part} not found in XML")
                                    else:
                                        raise ValueError(f"Element {part} not found in XML")
        except Exception as e:
            raise ValueError(f"Error navigating XML structure: {str(e)}")
    elif xml_type == 'XML2':
        # For XML2, we need to handle the structure:
        # CHITIEU_CHITIET_THUOC/DSACH_CHI_TIET_THUOC/CHI_TIET_THUOC
        try:
            # First, try to find CHI_TIET_THUOC directly (most specific element)
            chi_tiet_thuoc_elements = root.findall('.//CHI_TIET_THUOC')
            if chi_tiet_thuoc_elements:
                # Nếu có nhiều phần tử CHI_TIET_THUOC, chỉ lấy phần tử đầu tiên
                if len(chi_tiet_thuoc_elements) > 1:
                    print(f"Warning: Found {len(chi_tiet_thuoc_elements)} CHI_TIET_THUOC elements, using the first one")
                element = chi_tiet_thuoc_elements[0]
            else:
                # If not found, try to navigate through the path
                for i, part in enumerate(path_parts):
                    # Strip any namespace from the tag
                    part_tag = part.split('}')[-1] if '}' in part else part

                    # For the last part (CHI_TIET_THUOC), we want the first occurrence
                    if i == len(path_parts) - 1:
                        # Find all elements with this tag
                        elements = element.findall(f'.//{part_tag}')
                        if elements:
                            element = elements[0]
                        else:
                            # Try direct child
                            child = element.find(part_tag)
                            if child is not None:
                                element = child
                            else:
                                raise ValueError(f"Element {part} not found in XML")
                    else:
                        # For other parts, try direct child first
                        child = element.find(part_tag)
                        if child is not None:
                            element = child
                        else:
                            # Try with XPath
                            elements = element.findall(f'.//{part_tag}')
                            if elements:
                                element = elements[0]
                            else:
                                raise ValueError(f"Element {part} not found in XML")
        except Exception as e:
            raise ValueError(f"Error navigating XML structure: {str(e)}")
    else:
        # For other XML types, use the original logic
        for part in path_parts:
            element = element.find(part)
            if element is None:
                raise ValueError(f"Element {part} not found in XML")

    # Create model instance
    model_data = {}

    # Map XML tags to model fields
    for child in element:
        # Strip any namespace from the tag
        child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag

        # Xử lý trường STT đặc biệt
        if child_tag == 'STT':
            model_data['stt'] = int(child.text) if child.text and child.text.strip() else None
            continue

        field_name = xml_tag_to_field_name(child_tag)

        # Debug log
        print(f"XML tag: {child_tag}, Field name: {field_name}, Value: {child.text}")

        if hasattr(model_class, field_name):
            model_data[field_name] = child.text if child.text else ""
        else:
            print(f"Warning: Field '{field_name}' not found in model {model_class.__name__}")

    # Debug log for model data
    print(f"Model data: {model_data}")

    # Create and return model instance
    return model_class(**model_data)

def parse_multiple_xml_to_models(xml_type, xml_content):
    """
    Parse XML content with multiple elements and convert to Django models
    """
    model_class = XML_MODEL_MAP.get(xml_type)
    if not model_class:
        raise ValueError(f"Invalid XML type: {xml_type}")

    # Fix common XML declaration issues
    if '<?xml' in xml_content:
        # Fix invalid XML declaration
        if '<?xml version="encoding=' in xml_content:
            xml_content = xml_content.replace('<?xml version="encoding=', '<?xml version="1.0" encoding=')

        # Fix other potential XML declaration issues
        if not ('<?xml version=' in xml_content and 'encoding=' in xml_content):
            xml_content = xml_content.replace('<?xml', '<?xml version="1.0" encoding="utf-8"')

    # Parse XML
    try:
        root = ET.fromstring(xml_content)
    except ET.ParseError as e:
        # Try to remove the XML declaration and parse again
        if '<?xml' in xml_content:
            try:
                # Remove the XML declaration
                xml_content_no_decl = xml_content[xml_content.find('?>')+2:].strip()
                root = ET.fromstring(xml_content_no_decl)
            except ET.ParseError:
                raise ValueError(f"Invalid XML format: {str(e)}")
        else:
            raise ValueError(f"Invalid XML format: {str(e)}")

    # Get root tag path
    root_tag_path = XML_ROOT_TAG_MAP.get(xml_type)
    if not root_tag_path:
        raise ValueError(f"Root tag path not defined for XML type: {xml_type}")

    # Split path to get the container and item tags
    path_parts = root_tag_path.split('/')
    if len(path_parts) < 2:
        raise ValueError(f"Invalid root tag path for XML type: {xml_type}")

    # For XML2, find all CHI_TIET_THUOC elements
    if xml_type == 'XML2':
        item_tag = 'CHI_TIET_THUOC'
        elements = root.findall(f'.//{item_tag}')
        if not elements:
            raise ValueError(f"No {item_tag} elements found in XML")

        print(f"Found {len(elements)} {item_tag} elements")

        # Create model instances for each element
        model_instances = []
        for element in elements:
            # Create model instance
            model_data = {}

            # Map XML tags to model fields
            for child in element:
                # Strip any namespace from the tag
                child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag

                # Xử lý trường STT đặc biệt
                if child_tag == 'STT':
                    model_data['stt'] = int(child.text) if child.text and child.text.strip() else None
                    continue

                field_name = xml_tag_to_field_name(child_tag)

                # Debug log
                print(f"XML tag: {child_tag}, Field name: {field_name}, Value: {child.text}")

                if hasattr(model_class, field_name):
                    model_data[field_name] = child.text if child.text else ""
                else:
                    print(f"Warning: Field '{field_name}' not found in model {model_class.__name__}")

            # Debug log for model data
            print(f"Model data: {model_data}")

            # Create and add model instance
            if 'maLK' in model_data:  # Chỉ thêm nếu có mã liên kết
                model_instances.append(model_class(**model_data))

        return model_instances
    else:
        # For other XML types, use the original logic
        return [parse_xml_to_model(xml_type, xml_content)]

def export_model_to_xml(xml_type, model_instance):
    """
    Convert Django model to XML
    """
    # Get root tag path
    root_tag_path = XML_ROOT_TAG_MAP.get(xml_type)
    if not root_tag_path:
        raise ValueError(f"Root tag path not defined for XML type: {xml_type}")

    # Split path and create elements
    path_parts = root_tag_path.split('/')

    # Create root element
    root = ET.Element(path_parts[0])

    # Create nested elements if needed
    current = root
    for part in path_parts[1:]:
        new_element = ET.SubElement(current, part)
        current = new_element

    # Add fields to the element
    for field in model_instance._meta.fields:
        if field.name not in ['id', 'ngayTao', 'ngayChinhSua', 'trangThaiGuiBHXH']:
            value = getattr(model_instance, field.name)
            if value is not None:
                tag_name = field_name_to_xml_tag(field.name)
                field_element = ET.SubElement(current, tag_name)
                field_element.text = str(value)
            # For XML0, we need to include empty tags for optional fields
            elif xml_type == 'XML0':
                tag_name = field_name_to_xml_tag(field.name)
                field_element = ET.SubElement(current, tag_name)
                field_element.text = ""

    # Add XML declaration and convert to string
    xml_declaration = '<?xml version="1.0" encoding="utf-8" standalone="yes"?>\n'
    xml_content = ET.tostring(root, encoding='utf-8', method='xml').decode('utf-8')

    # Only add XML declaration for XML0
    if xml_type == 'XML0':
        return xml_declaration + xml_content
    else:
        return xml_content

def encode_xml_to_base64(xml_content):
    """
    Encode XML content to Base64
    """
    return base64.b64encode(xml_content.encode('utf-8')).decode('utf-8')

def decode_base64_to_xml(base64_content):
    """
    Decode Base64 content to XML
    """
    return base64.b64decode(base64_content).decode('utf-8')

def create_xml_file(xml_type, model_instance, ma_cskcb='64020'):
    """
    Create complete XML file with GIAMDINHHS structure
    """
    # For XML0, we can return the XML directly without the GIAMDINHHS wrapper
    if xml_type == 'XML0':
        return export_model_to_xml(xml_type, model_instance)

    # For other XML types, use the original logic with GIAMDINHHS structure
    # Create XML content
    xml_content = export_model_to_xml(xml_type, model_instance)

    # Encode to Base64
    base64_content = encode_xml_to_base64(xml_content)

    # Create GIAMDINHHS structure
    root = ET.Element('GIAMDINHHS')

    # Add THONGTINDONVI
    donvi = ET.SubElement(root, 'THONGTINDONVI')
    macskcb = ET.SubElement(donvi, 'MACSKCB')
    macskcb.text = ma_cskcb

    # Add THONGTINHOSO
    hoso = ET.SubElement(root, 'THONGTINHOSO')

    ngaylap = ET.SubElement(hoso, 'NGAYLAP')
    ngaylap.text = datetime.now().strftime('%Y%m%d%H%M')

    soluong = ET.SubElement(hoso, 'SOLUONGHOSO')
    soluong.text = '1'

    danhsach = ET.SubElement(hoso, 'DANHSACHHOSO')
    hs = ET.SubElement(danhsach, 'HOSO')

    filehoso = ET.SubElement(hs, 'FILEHOSO')
    loaihoso = ET.SubElement(filehoso, 'LOAIHOSO')
    loaihoso.text = xml_type
    noidung = ET.SubElement(filehoso, 'NOIDUNGFILE')
    noidung.text = base64_content

    # Convert to string
    return ET.tostring(root, encoding='utf-8', method='xml').decode('utf-8')

def parse_xml_file(xml_file):
    """
    Parse XML file and extract FILEHOSO elements or direct XML0 content
    """
    try:
        # Read the file content
        xml_file.seek(0)  # Reset file pointer
        xml_content = xml_file.read().decode('utf-8')

        # Fix common XML declaration issues
        if '<?xml' in xml_content:
            # Fix invalid XML declaration
            if '<?xml version="encoding=' in xml_content:
                xml_content = xml_content.replace('<?xml version="encoding=', '<?xml version="1.0" encoding=')

            # Fix other potential XML declaration issues
            if not ('<?xml version=' in xml_content and 'encoding=' in xml_content):
                xml_content = xml_content.replace('<?xml', '<?xml version="1.0" encoding="utf-8"')

        # Parse XML
        try:
            root = ET.fromstring(xml_content)
        except ET.ParseError as e:
            # Try to remove the XML declaration and parse again
            if '<?xml' in xml_content:
                # Remove the XML declaration
                xml_content_no_decl = xml_content[xml_content.find('?>')+2:].strip()
                root = ET.fromstring(xml_content_no_decl)
            else:
                raise
    except Exception as e:
        # If all else fails, try to parse the file directly
        xml_file.seek(0)  # Reset file pointer
        tree = ET.parse(xml_file)
        root = tree.getroot()

    # Check if this is an XML0 file (CHI_TIEU_TRANG_THAI_KCB root) or XML1 file (TONG_HOP root)
    # Strip any namespace from the tag
    root_tag = root.tag.split('}')[-1] if '}' in root.tag else root.tag

    if root_tag == 'CHI_TIEU_TRANG_THAI_KCB':
        # This is an XML0 file, return it directly
        xml_content = ET.tostring(root, encoding='utf-8', method='xml').decode('utf-8')
        return {'XML0': xml_content}

    if root_tag == 'TONG_HOP':
        # This is an XML1 file, return it directly
        xml_content = ET.tostring(root, encoding='utf-8', method='xml').decode('utf-8')
        return {'XML1': xml_content}

    # Check if the file contains XML0 structure
    dsach_trang_thai = root.find('.//DSACH_TRANG_THAI_KCB')
    if dsach_trang_thai is not None:
        # Create a new XML structure with the correct root
        new_root = ET.Element('CHI_TIEU_TRANG_THAI_KCB')
        new_dsach = ET.SubElement(new_root, 'DSACH_TRANG_THAI_KCB')

        # Copy all TRANG_THAI_KCB elements
        for trang_thai in dsach_trang_thai.findall('./TRANG_THAI_KCB'):
            ET.SubElement(new_dsach, 'TRANG_THAI_KCB', attrib=trang_thai.attrib).extend(trang_thai)

        # Convert to string
        xml_content = ET.tostring(new_root, encoding='utf-8', method='xml').decode('utf-8')
        return {'XML0': xml_content}

    # Check if the file contains TRANG_THAI_KCB elements directly
    trang_thai_elements = root.findall('.//TRANG_THAI_KCB')
    if trang_thai_elements:
        # Create a new XML structure with the correct root
        new_root = ET.Element('CHI_TIEU_TRANG_THAI_KCB')
        new_dsach = ET.SubElement(new_root, 'DSACH_TRANG_THAI_KCB')

        # Copy all TRANG_THAI_KCB elements
        for trang_thai in trang_thai_elements:
            ET.SubElement(new_dsach, 'TRANG_THAI_KCB', attrib=trang_thai.attrib).extend(trang_thai)

        # Convert to string
        xml_content = ET.tostring(new_root, encoding='utf-8', method='xml').decode('utf-8')
        return {'XML0': xml_content}

    # Check if the file contains THONG_TIN_HANH_CHINH elements directly
    hanh_chinh_elements = root.findall('.//THONG_TIN_HANH_CHINH')
    if hanh_chinh_elements:
        # Create a new XML structure with the correct root
        new_root = ET.Element('DSACH_THONG_TIN_HANH_CHINH')

        # Copy all THONG_TIN_HANH_CHINH elements
        for hanh_chinh in hanh_chinh_elements:
            ET.SubElement(new_root, 'THONG_TIN_HANH_CHINH', attrib=hanh_chinh.attrib).extend(hanh_chinh)

        # Convert to string
        xml_content = ET.tostring(new_root, encoding='utf-8', method='xml').decode('utf-8')
        return {'XML1': xml_content}

    # Check if the file contains CHI_TIET_THUOC elements directly (XML2)
    chi_tiet_thuoc_elements = root.findall('.//CHI_TIET_THUOC')
    if chi_tiet_thuoc_elements:
        # Create a new XML structure with the correct root
        new_root = ET.Element('CHITIEU_CHITIET_THUOC')
        new_dsach = ET.SubElement(new_root, 'DSACH_CHI_TIET_THUOC')

        # Copy all CHI_TIET_THUOC elements
        for chi_tiet_thuoc in chi_tiet_thuoc_elements:
            ET.SubElement(new_dsach, 'CHI_TIET_THUOC', attrib=chi_tiet_thuoc.attrib).extend(chi_tiet_thuoc)

        # Convert to string
        xml_content = ET.tostring(new_root, encoding='utf-8', method='xml').decode('utf-8')
        return {'XML2': xml_content}

    # For other XML types, find all FILEHOSO elements
    result = {}
    for filehoso in root.findall('.//FILEHOSO'):
        loaihoso = filehoso.find('LOAIHOSO')
        noidungfile = filehoso.find('NOIDUNGFILE')

        if loaihoso is not None and noidungfile is not None and loaihoso.text and noidungfile.text:
            # Decode Base64
            xml_content = decode_base64_to_xml(noidungfile.text)

            # Add to result
            result[loaihoso.text] = xml_content

    return result
