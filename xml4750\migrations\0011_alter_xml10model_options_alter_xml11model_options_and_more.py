# Generated by Django 4.2.7 on 2025-05-24 10:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('xml4750', '0010_auto_20250524_1747'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='xml10model',
            options={'verbose_name': 'Chỉ tiêu dữ liệu giấy chứng nhận nghỉ dưỡng thai', 'verbose_name_plural': 'Chỉ tiêu dữ liệu giấy chứng nhận nghỉ dưỡng thai'},
        ),
        migrations.AlterModelOptions(
            name='xml11model',
            options={'verbose_name': 'Chỉ tiêu dữ liệu giấy chứng nhận nghỉ việc hưởng bảo hiểm xã hội', 'verbose_name_plural': 'Chỉ tiêu dữ liệu giấy chứng nhận nghỉ việc hưởng bảo hiểm xã hội'},
        ),
        migrations.AlterModelOptions(
            name='xml12model',
            options={'verbose_name': 'Chỉ tiêu dữ liệu giám định y khoa', 'verbose_name_plural': 'Chỉ tiêu dữ liệu giám định y khoa'},
        ),
        migrations.AlterModelOptions(
            name='xml13model',
            options={'verbose_name': 'Chỉ tiêu chi tiết thuốc', 'verbose_name_plural': 'Chỉ tiêu chi tiết thuốc'},
        ),
        migrations.AlterModelOptions(
            name='xml14model',
            options={'verbose_name': 'Chỉ tiêu dữ liệu giấy chuyển tuyến', 'verbose_name_plural': 'Chỉ tiêu dữ liệu giấy chuyển tuyến'},
        ),
        migrations.AlterModelOptions(
            name='xml15model',
            options={'verbose_name': 'Chỉ tiêu dữ liệu giấy hẹn khám', 'verbose_name_plural': 'Chỉ tiêu dữ liệu giấy hẹn khám'},
        ),
        migrations.AlterModelOptions(
            name='xml3model',
            options={'verbose_name': 'Chỉ tiêu chi tiết dịch vụ kỹ thuật và vật tư y tế', 'verbose_name_plural': 'Chỉ tiêu chi tiết dịch vụ kỹ thuật và vật tư y tế'},
        ),
        migrations.AlterModelOptions(
            name='xml4model',
            options={'verbose_name': 'Chỉ tiêu chi tiết dịch vụ cận lâm sàng', 'verbose_name_plural': 'Chỉ tiêu chi tiết dịch vụ cận lâm sàng'},
        ),
        migrations.AlterModelOptions(
            name='xml5model',
            options={'verbose_name': 'Chỉ tiêu chi tiết diễn biến lâm sàng', 'verbose_name_plural': 'Chỉ tiêu chi tiết diễn biến lâm sàng'},
        ),
        migrations.AlterModelOptions(
            name='xml6model',
            options={'verbose_name': 'Chỉ tiêu hồ sơ bệnh án chăm sóc và điều trị HIV/AIDS', 'verbose_name_plural': 'Chỉ tiêu hồ sơ bệnh án chăm sóc và điều trị HIV/AIDS'},
        ),
        migrations.AlterModelOptions(
            name='xml7model',
            options={'verbose_name': 'Chỉ tiêu dữ liệu giấy ra viện', 'verbose_name_plural': 'Chỉ tiêu dữ liệu giấy ra viện'},
        ),
        migrations.AlterModelOptions(
            name='xml8model',
            options={'verbose_name': 'Chỉ tiêu dữ liệu tóm tắt hồ sơ bệnh án', 'verbose_name_plural': 'Chỉ tiêu dữ liệu tóm tắt hồ sơ bệnh án'},
        ),
        migrations.AlterModelOptions(
            name='xml9model',
            options={'verbose_name': 'Chỉ tiêu dữ liệu giấy chứng sinh', 'verbose_name_plural': 'Chỉ tiêu dữ liệu giấy chứng sinh'},
        ),
        migrations.RenameField(
            model_name='xml6model',
            old_name='kqDTLao',
            new_name='ketQuaDTriLao',
        ),
        migrations.RenameField(
            model_name='xml8model',
            old_name='tomTatKQ',
            new_name='tomtatKQ',
        ),
        migrations.RemoveField(
            model_name='xml10model',
            name='ghiChu',
        ),
        migrations.RemoveField(
            model_name='xml10model',
            name='ngayChinhSua',
        ),
        migrations.RemoveField(
            model_name='xml10model',
            name='ngayTao',
        ),
        migrations.RemoveField(
            model_name='xml10model',
            name='trangThaiGuiBHXH',
        ),
        migrations.RemoveField(
            model_name='xml11model',
            name='ghiChu',
        ),
        migrations.RemoveField(
            model_name='xml11model',
            name='ngayChinhSua',
        ),
        migrations.RemoveField(
            model_name='xml11model',
            name='ngayTao',
        ),
        migrations.RemoveField(
            model_name='xml11model',
            name='trangThaiGuiBHXH',
        ),
        migrations.RemoveField(
            model_name='xml12model',
            name='ghiChu',
        ),
        migrations.RemoveField(
            model_name='xml12model',
            name='maLK',
        ),
        migrations.RemoveField(
            model_name='xml12model',
            name='ngayChinhSua',
        ),
        migrations.RemoveField(
            model_name='xml12model',
            name='ngayTao',
        ),
        migrations.RemoveField(
            model_name='xml12model',
            name='trangThaiGuiBHXH',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='chanDoanRV',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='ghiChu',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='giayChuyenTuyen',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='gioiTinh',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='gtTheDen',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='hoTen',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='huongDieuTri',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='maCSKCB',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='maNoiDen',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='maNoiDi',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='maTheBHYT',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='ngayChinhSua',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='ngaySinh',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='ngayTao',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='qtBenhLy',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='soChuyenTuyen',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='soHoSo',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='trangThaiGuiBHXH',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='diaChi',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='ghiChu',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='maBenhChinh',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='maBenhKT',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='ngayChinhSua',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='ngayHenKL',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='ngayRa',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='ngayTao',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='ngayVao',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='soGiayHenKL',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='trangThaiGuiBHXH',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='LoaiDTriLao',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='ketQuaDTriLao',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='maBN',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='ngayBDDTriLao',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='ngayChinhSua',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='ngayKTDTriLao',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='ngayTao',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='phacDoDTriLao',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='phanLoaiLaoHIV',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='phanLoaiLaoTS',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='phanLoaiLaoViTri',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='soCCCD',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='trangThaiGuiBHXH',
        ),
        migrations.RemoveField(
            model_name='xml2model',
            name='ngayChinhSua',
        ),
        migrations.RemoveField(
            model_name='xml2model',
            name='ngayTao',
        ),
        migrations.RemoveField(
            model_name='xml2model',
            name='trangThaiGuiBHXH',
        ),
        migrations.RemoveField(
            model_name='xml3model',
            name='ghiChu',
        ),
        migrations.RemoveField(
            model_name='xml3model',
            name='ngayChinhSua',
        ),
        migrations.RemoveField(
            model_name='xml3model',
            name='ngayTao',
        ),
        migrations.RemoveField(
            model_name='xml3model',
            name='trangThaiGuiBHXH',
        ),
        migrations.RemoveField(
            model_name='xml4model',
            name='csbtDen',
        ),
        migrations.RemoveField(
            model_name='xml4model',
            name='csbtTu',
        ),
        migrations.RemoveField(
            model_name='xml4model',
            name='ghiChu',
        ),
        migrations.RemoveField(
            model_name='xml4model',
            name='maKhoa',
        ),
        migrations.RemoveField(
            model_name='xml4model',
            name='maMay',
        ),
        migrations.RemoveField(
            model_name='xml4model',
            name='ngayChinhSua',
        ),
        migrations.RemoveField(
            model_name='xml4model',
            name='ngayTao',
        ),
        migrations.RemoveField(
            model_name='xml4model',
            name='tenBSDocKQ',
        ),
        migrations.RemoveField(
            model_name='xml4model',
            name='trangThaiGuiBHXH',
        ),
        migrations.RemoveField(
            model_name='xml5model',
            name='dienBien',
        ),
        migrations.RemoveField(
            model_name='xml5model',
            name='ghiChu',
        ),
        migrations.RemoveField(
            model_name='xml5model',
            name='maBacSi',
        ),
        migrations.RemoveField(
            model_name='xml5model',
            name='maKhoa',
        ),
        migrations.RemoveField(
            model_name='xml5model',
            name='ngayChinhSua',
        ),
        migrations.RemoveField(
            model_name='xml5model',
            name='ngayKham',
        ),
        migrations.RemoveField(
            model_name='xml5model',
            name='ngayTao',
        ),
        migrations.RemoveField(
            model_name='xml5model',
            name='tenBacSi',
        ),
        migrations.RemoveField(
            model_name='xml5model',
            name='tenKhoa',
        ),
        migrations.RemoveField(
            model_name='xml5model',
            name='trangThaiGuiBHXH',
        ),
        migrations.RemoveField(
            model_name='xml6model',
            name='ghiChu',
        ),
        migrations.RemoveField(
            model_name='xml6model',
            name='maPhaDoDieuTriBD',
        ),
        migrations.RemoveField(
            model_name='xml6model',
            name='ngayChinhSua',
        ),
        migrations.RemoveField(
            model_name='xml6model',
            name='ngayTao',
        ),
        migrations.RemoveField(
            model_name='xml6model',
            name='trangThaiGuiBHXH',
        ),
        migrations.RemoveField(
            model_name='xml7model',
            name='ngayChinhSua',
        ),
        migrations.RemoveField(
            model_name='xml7model',
            name='ngayTao',
        ),
        migrations.RemoveField(
            model_name='xml7model',
            name='trangThaiGuiBHXH',
        ),
        migrations.RemoveField(
            model_name='xml8model',
            name='ngayChinhSua',
        ),
        migrations.RemoveField(
            model_name='xml8model',
            name='ngayTao',
        ),
        migrations.RemoveField(
            model_name='xml8model',
            name='trangThaiGuiBHXH',
        ),
        migrations.RemoveField(
            model_name='xml9model',
            name='hotenNND',
        ),
        migrations.RemoveField(
            model_name='xml9model',
            name='ngayChinhSua',
        ),
        migrations.RemoveField(
            model_name='xml9model',
            name='ngayTao',
        ),
        migrations.RemoveField(
            model_name='xml9model',
            name='trangThaiGuiBHXH',
        ),
        migrations.AddField(
            model_name='xml10model',
            name='duPhong',
            field=models.TextField(blank=True, null=True, verbose_name='Dự phòng'),
        ),
        migrations.AddField(
            model_name='xml10model',
            name='maTTDV',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã Thủ trưởng đơn vị'),
        ),
        migrations.AddField(
            model_name='xml11model',
            name='duPhong',
            field=models.TextField(blank=True, null=True, verbose_name='Dự phòng'),
        ),
        migrations.AddField(
            model_name='xml11model',
            name='hoTenCha',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Họ tên cha'),
        ),
        migrations.AddField(
            model_name='xml11model',
            name='hoTenMe',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Họ tên mẹ'),
        ),
        migrations.AddField(
            model_name='xml11model',
            name='maBS',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã bác sĩ'),
        ),
        migrations.AddField(
            model_name='xml11model',
            name='maTTDV',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã Thủ trưởng đơn vị'),
        ),
        migrations.AddField(
            model_name='xml11model',
            name='maTheTam',
            field=models.CharField(blank=True, max_length=15, null=True, verbose_name='Mã thẻ tạm'),
        ),
        migrations.AddField(
            model_name='xml11model',
            name='mauSo',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Mã mẫu số'),
        ),
        migrations.AddField(
            model_name='xml11model',
            name='ngayCT',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày chứng từ (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='dangHuongCheDo',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Dạng hướng chế độ'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='dangKhuyetTat',
            field=models.IntegerField(blank=True, null=True, verbose_name='Dạng khuyết tật'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='deNghi',
            field=models.TextField(blank=True, null=True, verbose_name='Đề nghị'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='dienThoai',
            field=models.CharField(blank=True, max_length=15, null=True, verbose_name='Điện thoại'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='duPhong',
            field=models.TextField(blank=True, null=True, verbose_name='Dự phòng'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='duocXacDinh',
            field=models.TextField(blank=True, null=True, verbose_name='Được xác định'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='gioiThieuCua',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Giới thiệu của'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='ketQuaKham',
            field=models.TextField(blank=True, null=True, verbose_name='Kết quả khám'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='khamGiamDinh',
            field=models.IntegerField(blank=True, null=True, verbose_name='Khám giám định'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='maDoiTuong',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Mã đối tượng'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='maDonVi',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Mã đơn vị'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='mucDoKhuyetTat',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mức độ khuyết tật'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='ngayCapCCCD',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày cấp CCCD (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='ngayChungTu',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày chứng từ (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='ngayDeNghi',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày đề nghị'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='ngheNghiep',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Nghề nghiệp'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='noiCapCCCD',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Nơi cấp CCCD'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='soBienBan',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Số biên bản'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='soGiayGioiThieu',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Số giấy giới thiệu'),
        ),
        migrations.AddField(
            model_name='xml12model',
            name='soVanBanCanCu',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Số văn bản căn cứ'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='donGiaBH',
            field=models.FloatField(blank=True, null=True, verbose_name='Đơn giá bảo hiểm'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='donGiaBV',
            field=models.FloatField(blank=True, null=True, verbose_name='Đơn giá bệnh viện'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='donViTinh',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Đơn vị tính'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='duPhong',
            field=models.TextField(blank=True, null=True, verbose_name='Dự phòng'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='duongDung',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Đường dùng'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='hamLuong',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Hàm lượng'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='lieuDung',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Liều dùng'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maBacSi',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã bác sĩ'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maBenh',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã bệnh'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maBenhYHCT',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã bệnh YHCT'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maKhoa',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Mã khoa'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maNhom',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mã nhóm theo chi phí'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maPTTT',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mã Phương thức thanh toán'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maThuoc',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã thuốc'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='mucHuong',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mức hưởng'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='ngayDung',
            field=models.CharField(blank=True, max_length=12, null=True, verbose_name='Ngày dùng (yyyyMMddHHmm)'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='ngayYL',
            field=models.CharField(blank=True, max_length=12, null=True, verbose_name='Ngày y lệnh (yyyyMMddHHmm)'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='nguoiThucHien',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Người thực hiện'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='soLuong',
            field=models.FloatField(blank=True, null=True, verbose_name='Số lượng'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='stt',
            field=models.IntegerField(blank=True, null=True, verbose_name='Số thứ tự'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='tBHTT',
            field=models.FloatField(blank=True, null=True, verbose_name='Bảo hiểm thanh toán'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='tBNCCT',
            field=models.FloatField(blank=True, null=True, verbose_name='Bệnh nhân cùng chi trả'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='tBNTT',
            field=models.FloatField(blank=True, null=True, verbose_name='Bệnh nhân thanh toán'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='tNguonKhac',
            field=models.FloatField(blank=True, null=True, verbose_name='Thanh toán nguồn khác'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='tNguonKhacCL',
            field=models.FloatField(blank=True, null=True, verbose_name='Các nguồn còn lại'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='tNguonKhacNSNN',
            field=models.FloatField(blank=True, null=True, verbose_name='Tổng nguồn từ ngân sách nhà nước'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='tNguonKhacVTNN',
            field=models.FloatField(blank=True, null=True, verbose_name='Tồng nguồn từ nước ngoài'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='tNguonKhacVTTN',
            field=models.FloatField(blank=True, null=True, verbose_name='Tồng nguồn từ trong nước'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='tTranTT',
            field=models.FloatField(blank=True, null=True, verbose_name='Trần thanh toán'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='tenThuoc',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Tên thuốc'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='thanhTienBH',
            field=models.FloatField(blank=True, null=True, verbose_name='Thành tiền bảo hiểm'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='thanhTienBV',
            field=models.FloatField(blank=True, null=True, verbose_name='Thành tiền bệnh viện'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='ttThau',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Thông tin thầu'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='tyLeTT',
            field=models.FloatField(blank=True, null=True, verbose_name='Tỷ lệ thanh toán'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='tyLeTTBH',
            field=models.IntegerField(blank=True, null=True, verbose_name='Tỷ lệ thanh toán BHYT'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='duPhong',
            field=models.TextField(blank=True, null=True, verbose_name='Dự phòng'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='giayChuyenTuyen',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Giấy chuyển tuyến'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='huongDieuTri',
            field=models.TextField(blank=True, null=True, verbose_name='Hướng điều trị'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='maNoiDen',
            field=models.CharField(blank=True, max_length=5, null=True, verbose_name='Mã nơi đến'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='maNoiDi',
            field=models.CharField(blank=True, max_length=5, null=True, verbose_name='Mã nơi đi'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='maTTDV',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã Thủ trưởng đơn vị'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='maTheTam',
            field=models.CharField(blank=True, max_length=15, null=True, verbose_name='Mã thẻ tạm'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='ngayCT',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày chứng từ (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='qtBenhLy',
            field=models.TextField(blank=True, null=True, verbose_name='Quá trình bệnh lý'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='soChuyenTuyen',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Số chuyển tuyến'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='soHoSo',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Số hồ sơ'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='chanDoan',
            field=models.TextField(blank=True, null=True, verbose_name='Chẩn đoán'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='diaChi',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Địa chỉ'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='duPhong',
            field=models.TextField(blank=True, null=True, verbose_name='Dự phòng'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='gioiTinh',
            field=models.IntegerField(blank=True, null=True, verbose_name='Giới tính (1: Nam, 2: Nữ)'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='maBS',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã bác sĩ'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='maTTDV',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã Thủ trưởng đơn vị'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='maTheBHYT',
            field=models.TextField(blank=True, null=True, verbose_name='Mã thẻ BHYT'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='maTheTam',
            field=models.CharField(blank=True, max_length=15, null=True, verbose_name='Mã thẻ tạm'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='ngayCT',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày chứng từ (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='ngayHenKham',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày hẹn khám (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='ngaySinh',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày sinh (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='ppDieuTri',
            field=models.TextField(blank=True, null=True, verbose_name='Phương pháp điều trị'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='soPhieu',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Số phiếu'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='duPhong',
            field=models.TextField(blank=True, null=True, verbose_name='Dự phòng'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='goiVTYT',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Gói VTYT trong một lần sử dụng DVKT'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='maBenhYHCT',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã bệnh YHCT'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='maDichVu',
            field=models.CharField(default='', max_length=100, verbose_name='Mã DVKT/Khám/Giường'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='maGiuong',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Mã giường'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='maHieuSP',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã hiệu sản phẩm'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='maMay',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Mã máy'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='maPTTT',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mã Phương thức thanh toán'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='maPTTTQT',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã phẫu thuật thủ thuật quốc tế'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='maXangDau',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Mã loại xăng dầu'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='ngayTHYL',
            field=models.CharField(blank=True, max_length=12, null=True, verbose_name='Ngày thực hành y lệnh (yyyyMMddHHmm)'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='nguoiThucHien',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Người thực hiện'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='ppVoCam',
            field=models.CharField(blank=True, max_length=3, null=True, verbose_name='Phương pháp vô cảm'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='tNguonKhacCL',
            field=models.FloatField(blank=True, null=True, verbose_name='Các nguồn còn lại'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='tNguonKhacNSNN',
            field=models.FloatField(blank=True, null=True, verbose_name='Tổng nguồn từ ngân sách nhà nước'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='tNguonKhacVTNN',
            field=models.FloatField(blank=True, null=True, verbose_name='Tồng nguồn từ nước ngoài'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='tNguonKhacVTTN',
            field=models.FloatField(blank=True, null=True, verbose_name='Tồng nguồn từ trong nước'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='tTranTT',
            field=models.FloatField(blank=True, null=True, verbose_name='Trần thanh toán'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='taiSuDung',
            field=models.CharField(blank=True, max_length=1, null=True, verbose_name='Tái sử dụng'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='tenDichVu',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Tên DVKT/Khám/Giường'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='ttThau',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Thông tin thầu'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='tyLeTTDV',
            field=models.IntegerField(blank=True, null=True, verbose_name='Tỷ lệ thanh toán DVKT'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='vetThuongTP',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Vết thương tái phát'),
        ),
        migrations.AddField(
            model_name='xml3model',
            name='viTriThDVKT',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Vị trí thực hiện DVKT'),
        ),
        migrations.AddField(
            model_name='xml4model',
            name='duPhong',
            field=models.TextField(blank=True, null=True, verbose_name='Dự phòng'),
        ),
        migrations.AddField(
            model_name='xml5model',
            name='dienBienLS',
            field=models.TextField(blank=True, null=True, verbose_name='Diễn biến lâm sàng'),
        ),
        migrations.AddField(
            model_name='xml5model',
            name='duPhong',
            field=models.TextField(blank=True, null=True, verbose_name='Dự phòng'),
        ),
        migrations.AddField(
            model_name='xml5model',
            name='giaiDoanBenh',
            field=models.TextField(blank=True, null=True, verbose_name='Giai đoạn bệnh'),
        ),
        migrations.AddField(
            model_name='xml5model',
            name='nguoiThucHien',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Người thực hiện'),
        ),
        migrations.AddField(
            model_name='xml5model',
            name='thoiDiemDBLS',
            field=models.CharField(blank=True, max_length=12, null=True, verbose_name='Thời điểm diễn biến (yyyyMMddHHmm)'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='bdDTARV',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Bắt đầu điều trị ARV (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='diaChi',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Địa chỉ'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='duPhong',
            field=models.TextField(blank=True, null=True, verbose_name='Dự phòng'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='giaiDoanLamSang',
            field=models.IntegerField(blank=True, null=True, verbose_name='Giải đoạn làm sáng'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='gioiTinh',
            field=models.IntegerField(blank=True, null=True, verbose_name='Giới tính (1: Nam, 2: Nữ)'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='kqXNTLVR',
            field=models.IntegerField(blank=True, null=True, verbose_name='Kết quả xét nghiệm TLVR'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='lanXNPCR',
            field=models.IntegerField(blank=True, null=True, verbose_name='Lần xét nghiệm PCR'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='lyDoChuyenPhacDo',
            field=models.IntegerField(blank=True, null=True, verbose_name='Lý do chuyển phác đồ'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='maBacPhacDo',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mã bậc phác đồ'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='maBacPhacDoBD',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mã bậc phác đồ ban đầu'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='maCSKCB',
            field=models.CharField(blank=True, max_length=5, null=True, verbose_name='Mã cơ sở khám chữa bệnh'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='maKQXNPCR',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mã kết quả xét nghiệm PCR'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='maLoaiBN',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mã loại bệnh nhân'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='maLydoDtri',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mã lý do điều trị'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='maLydoXNTLVR',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mã lý do xét nghiệm TLVR'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='maPhacDoDieuTri',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Mã phác đồ điều trị'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='maPhacDoDieuTriBD',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Mã phác đồ điều trị ban đầu'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='maTinhTrangDK',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Mã tình trạng đăng ký'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='maXuTri',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mã xử trí'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='ngayBDDTriLao',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày bắt đầu điều trị lao (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='ngayBatDauDTCTX',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày bắt đầu điều trị ctx (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='ngayBatDauXuTri',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày bắt đầu xử trí (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='ngayChuyenPhacDo',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày chuyển phác đồ (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='ngayKQXNPCR',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày kết quả xét nghiệm PCR (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='ngayKQXNTLVR',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày kết quả xét nghiệm TLVR (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='ngayKTDTriLao',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày kết thúc điều trị lao (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='ngayKetThucXuTri',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày kết thúc xử trí (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='ngayNhanTTMangThai',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày nhận thông tin mang thai (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='ngayXNPCR',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày xét nghiệm PCR (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='ngayXNTLVR',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày xét nghiệm TLVR (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='nhomDoiTuong',
            field=models.IntegerField(blank=True, null=True, verbose_name='Nhóm đối tượng'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='sangLocLao',
            field=models.IntegerField(blank=True, null=True, verbose_name='Sàng lọc lao'),
        ),
        migrations.AddField(
            model_name='xml6model',
            name='soNgayCapThuocARV',
            field=models.IntegerField(blank=True, null=True, verbose_name='Số ngày cấp thuốc ARV'),
        ),
        migrations.AddField(
            model_name='xml7model',
            name='duPhong',
            field=models.TextField(blank=True, null=True, verbose_name='Dự phòng'),
        ),
        migrations.AddField(
            model_name='xml7model',
            name='maBS',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã bác sĩ'),
        ),
        migrations.AddField(
            model_name='xml7model',
            name='maTTDV',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã trung tâm dịch vụ'),
        ),
        migrations.AddField(
            model_name='xml7model',
            name='maTheTam',
            field=models.CharField(blank=True, max_length=15, null=True, verbose_name='Mã thẻ tạm'),
        ),
        migrations.AddField(
            model_name='xml7model',
            name='ngayCT',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày chứng từ (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml7model',
            name='ngoaiTruDenNgay',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngoại trú đến ngày (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml7model',
            name='ngoaiTruTuNgay',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngoại trú từ ngày (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml7model',
            name='soNgayNghi',
            field=models.IntegerField(blank=True, null=True, verbose_name='Số ngày nghỉ'),
        ),
        migrations.AddField(
            model_name='xml7model',
            name='tenBS',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Tên bác sĩ'),
        ),
        migrations.AddField(
            model_name='xml7model',
            name='thoiGianDinhChi',
            field=models.CharField(blank=True, max_length=12, null=True, verbose_name='Thời gian định chỉ (yyyyMMddHHmm)'),
        ),
        migrations.AddField(
            model_name='xml8model',
            name='donVi',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Đơn vị'),
        ),
        migrations.AddField(
            model_name='xml8model',
            name='duPhong',
            field=models.TextField(blank=True, null=True, verbose_name='Dự phòng'),
        ),
        migrations.AddField(
            model_name='xml8model',
            name='maLoaiKCB',
            field=models.CharField(blank=True, max_length=2, null=True, verbose_name='Mã loại KCB'),
        ),
        migrations.AddField(
            model_name='xml8model',
            name='maTTDV',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã trung tâm dịch vụ'),
        ),
        migrations.AddField(
            model_name='xml8model',
            name='maTheTam',
            field=models.CharField(blank=True, max_length=15, null=True, verbose_name='Mã thẻ tạm'),
        ),
        migrations.AddField(
            model_name='xml8model',
            name='ngayCT',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày chứng từ (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='duPhong',
            field=models.TextField(blank=True, null=True, verbose_name='Dự phòng'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='hoTenCha',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Họ tên cha'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='maBHXHNND',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Mã BHXH người nhà'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='maDanTocNND',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Mã dân tộc người nhà'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='maQuocTich',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Mã quốc tịch'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='maTTDV',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã trung tâm dịch vụ'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='maTheNND',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Mã thẻ người nhà'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='maTheTam',
            field=models.CharField(blank=True, max_length=15, null=True, verbose_name='Mã thẻ tạm'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='ngayCT',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày chứng từ (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='ngayCapCCCDNND',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày cấp CCCD người nhà (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='nguoiGhiPhieu',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Người ghi phiếu'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='noiCapCCCDNND',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Nơi cấp CCCD người nhà'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='quyenSo',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Quyển số'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='sinhConDuoi32Tuan',
            field=models.IntegerField(blank=True, null=True, verbose_name='Sinh con dưới 32 tuần'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='so',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Số'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='soConSong',
            field=models.IntegerField(blank=True, null=True, verbose_name='Số con sống'),
        ),
        migrations.AlterField(
            model_name='xml10model',
            name='chanDoanRV',
            field=models.TextField(verbose_name='Chẩn đoán ra viện'),
        ),
        migrations.AlterField(
            model_name='xml10model',
            name='denNgay',
            field=models.CharField(max_length=8, verbose_name='Đến ngày (yyyyMMdd)'),
        ),
        migrations.AlterField(
            model_name='xml10model',
            name='donVi',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Đơn vị'),
        ),
        migrations.AlterField(
            model_name='xml10model',
            name='maBS',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã bác sĩ'),
        ),
        migrations.AlterField(
            model_name='xml10model',
            name='maLK',
            field=models.CharField(max_length=100, verbose_name='Mã liên kết'),
        ),
        migrations.AlterField(
            model_name='xml10model',
            name='ngayCT',
            field=models.CharField(max_length=8, verbose_name='Ngày chứng từ (yyyyMMd)'),
        ),
        migrations.AlterField(
            model_name='xml10model',
            name='soCT',
            field=models.CharField(max_length=200, verbose_name='Số chứng từ'),
        ),
        migrations.AlterField(
            model_name='xml10model',
            name='soNgay',
            field=models.IntegerField(verbose_name='Số ngày'),
        ),
        migrations.AlterField(
            model_name='xml10model',
            name='soSeri',
            field=models.CharField(max_length=200, verbose_name='Số seri'),
        ),
        migrations.AlterField(
            model_name='xml10model',
            name='tenBS',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Tên bác sĩ'),
        ),
        migrations.AlterField(
            model_name='xml10model',
            name='tuNgay',
            field=models.CharField(max_length=8, verbose_name='Từ ngày (yyyyMMdd)'),
        ),
        migrations.AlterField(
            model_name='xml11model',
            name='chanDoanRV',
            field=models.TextField(verbose_name='Chẩn đoán ra viện'),
        ),
        migrations.AlterField(
            model_name='xml11model',
            name='denNgay',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Đến ngày (yyyyMMdd)'),
        ),
        migrations.AlterField(
            model_name='xml11model',
            name='donVi',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Đơn vị'),
        ),
        migrations.AlterField(
            model_name='xml11model',
            name='maLK',
            field=models.CharField(max_length=100, verbose_name='Mã liên kết'),
        ),
        migrations.AlterField(
            model_name='xml11model',
            name='maTheBHYT',
            field=models.TextField(verbose_name='Mã thẻ BHYT'),
        ),
        migrations.AlterField(
            model_name='xml11model',
            name='nguyenNhanDinhChi',
            field=models.TextField(blank=True, null=True, verbose_name='Nguyên nhân định chỉ'),
        ),
        migrations.AlterField(
            model_name='xml11model',
            name='ppDieuTri',
            field=models.TextField(blank=True, null=True, verbose_name='Phương pháp điều trị'),
        ),
        migrations.AlterField(
            model_name='xml11model',
            name='soCT',
            field=models.CharField(max_length=200, verbose_name='Số chứng từ'),
        ),
        migrations.AlterField(
            model_name='xml11model',
            name='soKCB',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Số khám chữa bệnh'),
        ),
        migrations.AlterField(
            model_name='xml11model',
            name='soSeri',
            field=models.CharField(max_length=200, verbose_name='Số seri'),
        ),
        migrations.AlterField(
            model_name='xml11model',
            name='tuNgay',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Từ ngày (yyyyMMdd)'),
        ),
        migrations.AlterField(
            model_name='xml11model',
            name='tuoiThai',
            field=models.IntegerField(blank=True, null=True, verbose_name='Tuổi thai (tuần)'),
        ),
        migrations.AlterField(
            model_name='xml12model',
            name='chucVu',
            field=models.IntegerField(blank=True, null=True, verbose_name='Chức vụ'),
        ),
        migrations.AlterField(
            model_name='xml12model',
            name='diaChi',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Địa chỉ'),
        ),
        migrations.AlterField(
            model_name='xml12model',
            name='hoTen',
            field=models.CharField(max_length=255, verbose_name='Họ tên'),
        ),
        migrations.AlterField(
            model_name='xml12model',
            name='maTheBHYT',
            field=models.CharField(max_length=20, verbose_name='Mã thẻ BHYT'),
        ),
        migrations.AlterField(
            model_name='xml12model',
            name='ngayHop',
            field=models.CharField(max_length=8, verbose_name='Ngày họp (yyyyMM)'),
        ),
        migrations.AlterField(
            model_name='xml12model',
            name='ngaySinh',
            field=models.CharField(max_length=8, verbose_name='Ngày sinh (yyyyMMdd)'),
        ),
        migrations.AlterField(
            model_name='xml12model',
            name='nguoiChuTri',
            field=models.CharField(max_length=255, verbose_name='Người chủ trì'),
        ),
        migrations.AlterField(
            model_name='xml12model',
            name='soCCCD',
            field=models.TextField(blank=True, null=True, verbose_name='Số căn cước công dân'),
        ),
        migrations.AlterField(
            model_name='xml12model',
            name='tongTyLeTTCT',
            field=models.IntegerField(verbose_name='Tổng tỷ lệ thương tật (%)'),
        ),
        migrations.AlterField(
            model_name='xml12model',
            name='tyLeTTCTCu',
            field=models.IntegerField(blank=True, null=True, verbose_name='Tỷ lệ thương tật cũ (%)'),
        ),
        migrations.AlterField(
            model_name='xml12model',
            name='tyLeTTCTMoi',
            field=models.IntegerField(blank=True, null=True, verbose_name='Tỷ lệ thương tật mới (%)'),
        ),
        migrations.AlterField(
            model_name='xml13model',
            name='maLK',
            field=models.CharField(max_length=100, verbose_name='Mã liên kết'),
        ),
        migrations.AlterField(
            model_name='xml14model',
            name='chanDoanRV',
            field=models.TextField(verbose_name='Chẩn đoán ra viện'),
        ),
        migrations.AlterField(
            model_name='xml14model',
            name='gioiTinh',
            field=models.IntegerField(verbose_name='Giới tính (1: Nam, 2: Nữ)'),
        ),
        migrations.AlterField(
            model_name='xml14model',
            name='gtTheDen',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Giá trị thẻ đến (yyyyMMdd)'),
        ),
        migrations.AlterField(
            model_name='xml14model',
            name='hoTen',
            field=models.CharField(max_length=255, verbose_name='Họ tên'),
        ),
        migrations.AlterField(
            model_name='xml14model',
            name='maCSKCB',
            field=models.CharField(max_length=5, verbose_name='Mã cơ sở KCB'),
        ),
        migrations.AlterField(
            model_name='xml14model',
            name='maLK',
            field=models.CharField(max_length=100, verbose_name='Mã liên kết'),
        ),
        migrations.AlterField(
            model_name='xml14model',
            name='maTheBHYT',
            field=models.TextField(verbose_name='Mã thẻ BHYT'),
        ),
        migrations.AlterField(
            model_name='xml14model',
            name='ngaySinh',
            field=models.CharField(max_length=8, verbose_name='Ngày sinh (yyyyMMdd)'),
        ),
        migrations.AlterField(
            model_name='xml15model',
            name='ghiChu',
            field=models.TextField(blank=True, null=True, verbose_name='Ghi chú'),
        ),
        migrations.AlterField(
            model_name='xml15model',
            name='hoTen',
            field=models.CharField(max_length=255, verbose_name='Họ tên'),
        ),
        migrations.AlterField(
            model_name='xml15model',
            name='maLK',
            field=models.CharField(max_length=100, verbose_name='Mã liên kết'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='donGiaBH',
            field=models.FloatField(blank=True, null=True, verbose_name='Đơn giá bảo hiểm'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='donGiaBV',
            field=models.FloatField(blank=True, null=True, verbose_name='Đơn giá bệnh viện'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='donViTinh',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Đơn vị tính'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='maBacSi',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã bác sĩ'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='maBenh',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã bệnh'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='maKhoa',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Mã khoa'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='maLK',
            field=models.CharField(max_length=100, verbose_name='Mã liên kết'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='maNhom',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mã nhóm theo chi phí'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='maVatTu',
            field=models.CharField(max_length=255, verbose_name='Mã vật tư y tế'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='mucHuong',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mức hưởng'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='ngayKQ',
            field=models.CharField(blank=True, max_length=12, null=True, verbose_name='Ngày kết quả (yyyyMMddHHmm)'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='ngayYL',
            field=models.CharField(blank=True, max_length=12, null=True, verbose_name='Ngày y lệnh (yyyyMMddHHmm)'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='soLuong',
            field=models.FloatField(blank=True, null=True, verbose_name='Số lượng'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='tenVatTu',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Tên vật tư y tế'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='thanhTienBH',
            field=models.FloatField(blank=True, null=True, verbose_name='Thành tiền bảo hiểm'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='thanhTienBV',
            field=models.FloatField(blank=True, null=True, verbose_name='Thành tiền bệnh viện'),
        ),
        migrations.AlterField(
            model_name='xml3model',
            name='tyLeTTBH',
            field=models.IntegerField(blank=True, null=True, verbose_name='Tỷ lệ thanh toán BHYT'),
        ),
        migrations.AlterField(
            model_name='xml4model',
            name='donViDo',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Đơn vị đo'),
        ),
        migrations.AlterField(
            model_name='xml4model',
            name='giaTri',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Giá trị'),
        ),
        migrations.AlterField(
            model_name='xml4model',
            name='ketLuan',
            field=models.TextField(blank=True, null=True, verbose_name='Kết luận'),
        ),
        migrations.AlterField(
            model_name='xml4model',
            name='maBSDocKQ',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã bác sĩ đọc kết quả'),
        ),
        migrations.AlterField(
            model_name='xml4model',
            name='maChiSo',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã chỉ số'),
        ),
        migrations.AlterField(
            model_name='xml4model',
            name='maDichVu',
            field=models.CharField(max_length=100, verbose_name='Mã dịch vụ'),
        ),
        migrations.AlterField(
            model_name='xml4model',
            name='maLK',
            field=models.CharField(max_length=100, verbose_name='Mã liên kết'),
        ),
        migrations.AlterField(
            model_name='xml4model',
            name='moTa',
            field=models.TextField(blank=True, null=True, verbose_name='Mô tả'),
        ),
        migrations.AlterField(
            model_name='xml4model',
            name='ngayKQ',
            field=models.CharField(blank=True, max_length=12, null=True, verbose_name='Ngày kết quả (yyyyMMddHHmm)'),
        ),
        migrations.AlterField(
            model_name='xml4model',
            name='tenChiSo',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Tên chỉ số'),
        ),
        migrations.AlterField(
            model_name='xml5model',
            name='maLK',
            field=models.CharField(max_length=100, verbose_name='Mã liên kết'),
        ),
        migrations.AlterField(
            model_name='xml6model',
            name='maLK',
            field=models.CharField(max_length=100, verbose_name='Mã liên kết'),
        ),
        migrations.AlterField(
            model_name='xml6model',
            name='maTheBHYT',
            field=models.TextField(verbose_name='Mã thẻ BHYT'),
        ),
        migrations.AlterField(
            model_name='xml6model',
            name='ngayKDHIV',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày khẳng định HIV (yyyyMMdd)'),
        ),
        migrations.AlterField(
            model_name='xml6model',
            name='ngaySinh',
            field=models.CharField(blank=True, max_length=12, null=True, verbose_name='Ngày sinh (yyyyMMddHHmm)'),
        ),
        migrations.AlterField(
            model_name='xml6model',
            name='noiBDDTARV',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Nơi bắt đầu điều trị ARV'),
        ),
        migrations.AlterField(
            model_name='xml6model',
            name='noiLayMauXN',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Nơi lấy mẫu xét nghiệm'),
        ),
        migrations.AlterField(
            model_name='xml6model',
            name='noiXNKD',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Nơi xét nghiệm khẳng định'),
        ),
        migrations.AlterField(
            model_name='xml6model',
            name='phacDoDtriLao',
            field=models.IntegerField(blank=True, null=True, verbose_name='Phác đồ điều trị lao'),
        ),
        migrations.AlterField(
            model_name='xml6model',
            name='soCCCD',
            field=models.TextField(blank=True, null=True, verbose_name='Số căn cước công dân'),
        ),
        migrations.AlterField(
            model_name='xml7model',
            name='chanDoanRV',
            field=models.CharField(blank=True, max_length=1500, null=True, verbose_name='Chẩn đoán ra viện'),
        ),
        migrations.AlterField(
            model_name='xml7model',
            name='ghiChu',
            field=models.CharField(blank=True, max_length=1500, null=True, verbose_name='Ghi chú'),
        ),
        migrations.AlterField(
            model_name='xml7model',
            name='maKhoaRV',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Mã khoa ra viện'),
        ),
        migrations.AlterField(
            model_name='xml7model',
            name='maLK',
            field=models.CharField(max_length=100, verbose_name='Mã liên kết'),
        ),
        migrations.AlterField(
            model_name='xml7model',
            name='maYTe',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Mã y tế'),
        ),
        migrations.AlterField(
            model_name='xml7model',
            name='ngayRa',
            field=models.CharField(blank=True, max_length=12, null=True, verbose_name='Ngày ra (yyyyMMddHHmm)'),
        ),
        migrations.AlterField(
            model_name='xml7model',
            name='ngayVao',
            field=models.CharField(blank=True, max_length=12, null=True, verbose_name='Ngày vào (yyyyMMddHHmm)'),
        ),
        migrations.AlterField(
            model_name='xml7model',
            name='nguyenNhanDinhChi',
            field=models.TextField(blank=True, null=True, verbose_name='Nguyên nhân định chỉ'),
        ),
        migrations.AlterField(
            model_name='xml7model',
            name='ppDieuTri',
            field=models.TextField(blank=True, null=True, verbose_name='Phương pháp điều trị'),
        ),
        migrations.AlterField(
            model_name='xml7model',
            name='soLuuTru',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Số lưu trữ'),
        ),
        migrations.AlterField(
            model_name='xml7model',
            name='tuoiThai',
            field=models.IntegerField(blank=True, null=True, verbose_name='Tuổi thai (tuần)'),
        ),
        migrations.AlterField(
            model_name='xml8model',
            name='chanDoanRV',
            field=models.TextField(blank=True, null=True, verbose_name='Chẩn đoán ra viện'),
        ),
        migrations.AlterField(
            model_name='xml8model',
            name='chanDoanVao',
            field=models.TextField(blank=True, null=True, verbose_name='Chẩn đoán vào'),
        ),
        migrations.AlterField(
            model_name='xml8model',
            name='ghiChu',
            field=models.TextField(blank=True, null=True, verbose_name='Ghi chú'),
        ),
        migrations.AlterField(
            model_name='xml8model',
            name='hoTenMe',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Họ tên mẹ'),
        ),
        migrations.AlterField(
            model_name='xml8model',
            name='ngayConChet',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày con chết (yyyyMMdd)'),
        ),
        migrations.AlterField(
            model_name='xml8model',
            name='ngayRa',
            field=models.CharField(blank=True, max_length=12, null=True, verbose_name='Ngày ra (yyyyMMddHHmm)'),
        ),
        migrations.AlterField(
            model_name='xml8model',
            name='ngaySinhCon',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày sinh con (yyyyMMdd)'),
        ),
        migrations.AlterField(
            model_name='xml8model',
            name='ngayVao',
            field=models.CharField(blank=True, max_length=12, null=True, verbose_name='Ngày vào (yyyyMMddHHmm)'),
        ),
        migrations.AlterField(
            model_name='xml8model',
            name='ppDieuTri',
            field=models.TextField(blank=True, null=True, verbose_name='Phương pháp điều trị'),
        ),
        migrations.AlterField(
            model_name='xml9model',
            name='ghiChu',
            field=models.TextField(blank=True, null=True, verbose_name='Ghi chú'),
        ),
        migrations.AlterField(
            model_name='xml9model',
            name='hoTenCon',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Họ tên con'),
        ),
        migrations.AlterField(
            model_name='xml9model',
            name='maLK',
            field=models.CharField(max_length=100, verbose_name='Mã liên kết'),
        ),
        migrations.AlterField(
            model_name='xml9model',
            name='ngaySinhCon',
            field=models.CharField(blank=True, max_length=12, null=True, verbose_name='Ngày sinh con (yyyyMMddHHmm)'),
        ),
        migrations.AlterField(
            model_name='xml9model',
            name='ngaySinhNND',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày sinh người nhà (yyyyMMdd)'),
        ),
        migrations.AlterField(
            model_name='xml9model',
            name='nguoiDoDe',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Người đỡ đẻ'),
        ),
        migrations.AlterField(
            model_name='xml9model',
            name='noiCuTruNND',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Nơi cư trú người nhà'),
        ),
        migrations.AlterField(
            model_name='xml9model',
            name='noiSinhCon',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Nơi sinh con'),
        ),
        migrations.AlterField(
            model_name='xml9model',
            name='soCCCDNND',
            field=models.TextField(blank=True, null=True, verbose_name='Số CCCD người nhà'),
        ),
        migrations.AlterField(
            model_name='xml9model',
            name='tinhTrangCon',
            field=models.TextField(blank=True, null=True, verbose_name='Tình trạng con'),
        ),
        migrations.AddField(
            model_name='xml9model',
            name='hoTenNND',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Họ tên người nhà'),
        ),
    ]
