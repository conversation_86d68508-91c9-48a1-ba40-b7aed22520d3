{% extends 'layouts/base.html' %}
{% load static %}
{% load xml_filters %}

{% block title %}Quản lý dữ liệu XML theo Quyết định 4750{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/sweetalert2-theme-bootstrap-4/bootstrap-4.min.css' %}">
<link href="https://unpkg.com/tabulator-tables@6.3.1/dist/css/tabulator.min.css" rel="stylesheet">
<link href="https://unpkg.com/tabulator-tables@6.3.1/dist/css/tabulator_bootstrap4.min.css" rel="stylesheet">
<style>
    /* Cải thiện hiển thị của bảng */
    .xml-table {
        width: 100%;
    }

    /* Cải thiện hiển thị của Tabulator */
    .xml-tabulator {
        width: 100%;
        height: calc(100vh - 350px);
        min-height: 300px;
        margin-bottom: 20px;
    }


    /* Đảm bảo tab-content kéo dài xuống cuối trang */
    .tab-content {
        display: block;
        min-height: 500px;
    }

    /* Đảm bảo tab-pane kéo dài xuống cuối trang */
    .tab-pane {
        display: none;
        width: 100%;
    }

    /* Đảm bảo tab-pane active hiển thị đúng */
    .tab-pane.active {
        display: block !important;
    }

    /* Đảm bảo các nút trong cột thao tác hiển thị đúng */
    .xml-table .btn-group {
        display: flex;
        flex-wrap: nowrap;
    }

    /* Cải thiện hiển thị tab */
    .tab-full-title {
        display: none;
    }

    .nav-tabs .nav-link.active .tab-full-title {
        display: inline;
    }

    .nav-tabs .nav-link.active .tab-short-title {
        font-weight: bold;
        margin-right: 5px;
    }

    .nav-tabs .nav-link {
        padding: 0.5rem 1rem;
    }

    /* Cải thiện hiển thị form */
    .form-group.required label:after {
        content: " *";
        color: red;
    }


    /* Hiển thị tooltip khi hover */
    [title] {
        position: relative;
        cursor: help;
        border-bottom: 1px dotted #666;
    }

    /* Hiệu ứng khi hover vào các ô có nội dung dài */
    .xml-table tbody td:hover {
        background-color: #f8f9fa;
    }

    /* Đảm bảo các nút trong cột thao tác hiển thị đúng */
    .action-column .btn {
        padding: 0.25rem 0.5rem;
        margin: 0 1px;
    }

    /* Cải thiện hiển thị header */
    .tabulator-header {
        background-color: #f4f6f9;
        font-weight: bold;
    }

    .tabulator-header-contents {
        text-align: center;
    }

    /* Cải thiện hiển thị hàng được chọn */
    .tabulator-row.tabulator-selected {
        background-color: #e2f0ff !important;
    }

    /* Cải thiện hiển thị khi hover vào hàng */
    .tabulator-row:hover {
        background-color: #f5f5f5 !important;
    }

    /* Đảm bảo bảng hiển thị đúng */
    .tabulator {
        width: 100% !important;
        margin-bottom: 15px;
        position: relative !important;
    }

    /* Đảm bảo bảng trong tab không hiển thị không chiếm không gian */
    .tab-pane:not(.active) .tabulator {
        display: none !important;
    }

    /* Đảm bảo các cột hiển thị đúng */
    .tabulator-col {
        background-color: #f4f6f9;
        border-right: 1px solid #dee2e6;
    }

    /* Đảm bảo các ô hiển thị đúng */
    .tabulator-cell {
        border-right: 1px solid #dee2e6;
        padding: 8px !important;
    }

    /* Cải thiện hiển thị phân trang */
    .pagination {
        justify-content: flex-end;
        margin-bottom: 0;
    }

    /* Cải thiện hiển thị thông tin số lượng mục */
    .pagination-info {
        margin-bottom: 0;
        padding-top: 8px;
    }

    .tabulator-edit-list .tabulator-edit-list-item {
        white-space: normal !important;
        word-wrap: break-word;
        overflow-wrap: break-word;
        max-width: 600px;
        line-height: 1.4;
        padding: 4px 8px;
    }
    /* Cải thiện hiển thị trên thiết bị di động */
    @media (max-width: 767px) {
        .pagination-info,
        .pagination {
            text-align: center;
            justify-content: center;
            margin-bottom: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Quản lý dữ liệu XML theo Quyết định 4750</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
                        <li class="breadcrumb-item active">Quản lý XML</li>
                    </ol>
                </div>
            </div>
        </div><!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Danh sách dữ liệu XML</h3>
                            <div class="card-tools">
                                <div class="input-group">
                                    <form method="GET" action="{% url 'xml4750:list_xml' %}" class="form-inline">
                                        <select name="xml_type" class="form-control mr-2">
                                            <option value="" {% if not xml_type %}selected{% endif %}>Tất cả</option>
                                            <option value="XML0" {% if xml_type == 'XML0' %}selected{% endif %}>XML0 - Bảng check-in</option>
                                            <option value="XML1" {% if xml_type == 'XML1' %}selected{% endif %}>XML1 - Thông tin hành chính</option>
                                            <option value="XML2" {% if xml_type == 'XML2' %}selected{% endif %}>XML2 - Thông tin thuốc</option>
                                            <option value="XML3" {% if xml_type == 'XML3' %}selected{% endif %}>XML3 - Thông tin VTYT</option>
                                            <option value="XML4" {% if xml_type == 'XML4' %}selected{% endif %}>XML4 - Thông tin xét nghiệm</option>
                                            <option value="XML5" {% if xml_type == 'XML5' %}selected{% endif %}>XML5 - Diễn biến lâm sàng</option>
                                            <option value="XML6" {% if xml_type == 'XML6' %}selected{% endif %}>XML6 - Thông tin điều trị HIV/AIDS</option>
                                            <option value="XML7" {% if xml_type == 'XML7' %}selected{% endif %}>XML7 - Thông tin thai sản</option>
                                            <option value="XML8" {% if xml_type == 'XML8' %}selected{% endif %}>XML8 - Thông tin trẻ sơ sinh</option>
                                            <option value="XML9" {% if xml_type == 'XML9' %}selected{% endif %}>XML9 - Thông tin người nuôi dưỡng</option>
                                            <option value="XML10" {% if xml_type == 'XML10' %}selected{% endif %}>XML10 - Giấy chứng nhận nghỉ việc hưởng BHXH</option>
                                            <option value="XML11" {% if xml_type == 'XML11' %}selected{% endif %}>XML11 - Giấy ra viện</option>
                                            <option value="XML12" {% if xml_type == 'XML12' %}selected{% endif %}>XML12 - Giấy chứng nhận giám định y khoa</option>
                                            <option value="XML13" {% if xml_type == 'XML13' %}selected{% endif %}>XML13 - Giấy chuyển viện</option>
                                            <option value="XML14" {% if xml_type == 'XML14' %}selected{% endif %}>XML14 - Giấy hẹn khám lại</option>
                                            <option value="XML15" {% if xml_type == 'XML15' %}selected{% endif %}>XML15 - Thông tin điều trị lao</option>
                                        </select>
                                        <input type="text" name="search" class="form-control mr-2" placeholder="Tìm kiếm..." value="{{ search_query }}">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                            <div class="mb-3">
                                <button type="button" class="btn btn-success" data-toggle="modal" data-target="#importXmlModal">
                                    <i class="fas fa-file-import"></i> Nhập XML
                                </button>
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exportXmlModal">
                                    <i class="fas fa-file-export"></i> Xuất XML
                                </button>
                                <button type="button" class="btn btn-danger delete-all-btn" data-type="{{ xml_type|default:'XML0' }}">
                                    <i class="fas fa-trash"></i> Xóa tất cả
                                </button>
                                <button type="button" class="btn btn-warning delete-selected-btn" data-type="{{ xml_type|default:'XML0' }}">
                                    <i class="fas fa-check-square"></i> Xóa đã chọn
                                </button>
                            </div>

                            <!-- Import XML Modal -->
                            <div class="modal fade" id="importXmlModal" tabindex="-1" role="dialog" aria-labelledby="importXmlModalLabel" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="importXmlModalLabel">Nhập dữ liệu XML</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form method="POST" action="{% url 'xml4750:import_xml' %}" enctype="multipart/form-data" id="import-xml-form">
                                            <div class="modal-body">
                                                {% csrf_token %}
                                                <div class="form-group required">
                                                    <label for="xml_type">Loại XML</label>
                                                    <select class="form-control" id="xml_type" name="xml_type" required>
                                                        <option value="">-- Chọn loại XML --</option>
                                                        <option value="XML0">XML0 - Bảng check-in</option>
                                                        <option value="XML1">XML1 - Thông tin hành chính</option>
                                                        <option value="XML2">XML2 - Thông tin thuốc</option>
                                                        <option value="XML3">XML3 - Thông tin VTYT</option>
                                                        <option value="XML4">XML4 - Thông tin xét nghiệm</option>
                                                        <option value="XML5">XML5 - Diễn biến lâm sàng</option>
                                                        <option value="XML6">XML6 - Thông tin điều trị HIV/AIDS</option>
                                                        <option value="XML7">XML7 - Thông tin thai sản</option>
                                                        <option value="XML8">XML8 - Thông tin trẻ sơ sinh</option>
                                                        <option value="XML9">XML9 - Thông tin người nuôi dưỡng</option>
                                                        <option value="XML10">XML10 - Giấy chứng nhận nghỉ việc hưởng BHXH</option>
                                                        <option value="XML11">XML11 - Giấy ra viện</option>
                                                        <option value="XML12">XML12 - Giấy chứng nhận giám định y khoa</option>
                                                        <option value="XML13">XML13 - Giấy chuyển viện</option>
                                                        <option value="XML14">XML14 - Giấy hẹn khám lại</option>
                                                        <option value="XML15">XML15 - Thông tin điều trị lao</option>
                                                    </select>
                                                </div>
                                                <div class="form-group required">
                                                    <label for="xml_file">File XML</label>
                                                    <div class="input-group">
                                                        <div class="custom-file">
                                                            <input type="file" class="custom-file-input" id="xml_file" name="xml_file" accept=".xml" required>
                                                            <label class="custom-file-label" for="xml_file">Chọn file</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                                                <button type="submit" class="btn btn-primary">Nhập dữ liệu</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Export XML Modal -->
                            <div class="modal fade" id="exportXmlModal" tabindex="-1" role="dialog" aria-labelledby="exportXmlModalLabel" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="exportXmlModalLabel">Xuất dữ liệu XML</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form method="POST" action="{% url 'xml4750:export_xml' %}" id="export-xml-form">
                                            <div class="modal-body">
                                                {% csrf_token %}
                                                <div class="form-group required">
                                                    <label for="export_xml_type">Loại XML</label>
                                                    <select class="form-control" id="export_xml_type" name="xml_type" required>
                                                        <option value="">-- Chọn loại XML --</option>
                                                        <option value="XML0">XML0 - Bảng check-in</option>
                                                        <option value="XML1">XML1 - Thông tin hành chính</option>
                                                        <option value="XML2">XML2 - Thông tin thuốc</option>
                                                        <option value="XML3">XML3 - Thông tin VTYT</option>
                                                        <option value="XML4">XML4 - Thông tin xét nghiệm</option>
                                                        <option value="XML5">XML5 - Diễn biến lâm sàng</option>
                                                        <option value="XML6">XML6 - Thông tin điều trị HIV/AIDS</option>
                                                        <option value="XML7">XML7 - Thông tin thai sản</option>
                                                        <option value="XML8">XML8 - Thông tin trẻ sơ sinh</option>
                                                        <option value="XML9">XML9 - Thông tin người nuôi dưỡng</option>
                                                        <option value="XML10">XML10 - Giấy chứng nhận nghỉ việc hưởng BHXH</option>
                                                        <option value="XML11">XML11 - Giấy ra viện</option>
                                                        <option value="XML12">XML12 - Giấy chứng nhận giám định y khoa</option>
                                                        <option value="XML13">XML13 - Giấy chuyển viện</option>
                                                        <option value="XML14">XML14 - Giấy hẹn khám lại</option>
                                                        <option value="XML15">XML15 - Thông tin điều trị lao</option>
                                                    </select>
                                                </div>
                                                <div class="form-group required">
                                                    <label for="export_maLK">Mã liên kết</label>
                                                    <input type="text" class="form-control" id="export_maLK" name="maLK" required>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                                                <button type="submit" class="btn btn-primary">Xuất dữ liệu</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- CSRF Token for AJAX requests -->
                            {% csrf_token %}

                            <ul class="nav nav-tabs" id="xmlTabs" role="tablist">
                                {% if not xml_type or xml_type == 'XML0' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if not xml_type or xml_type == 'XML0' %}active{% endif %}" id="xml0-tab" data-toggle="tab" href="#xml0" role="tab" aria-controls="xml0" aria-selected="true">
                                        <span class="tab-short-title">XML0</span>
                                        <span class="tab-full-title">Bảng check-in</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML1' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML1' %}active{% endif %}" id="xml1-tab" data-toggle="tab" href="#xml1" role="tab" aria-controls="xml1" aria-selected="false">
                                        <span class="tab-short-title">XML1</span>
                                        <span class="tab-full-title">Thông tin hành chính</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML2' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML2' %}active{% endif %}" id="xml2-tab" data-toggle="tab" href="#xml2" role="tab" aria-controls="xml2" aria-selected="false">
                                        <span class="tab-short-title">XML2</span>
                                        <span class="tab-full-title">Thông tin thuốc</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML3' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML3' %}active{% endif %}" id="xml3-tab" data-toggle="tab" href="#xml3" role="tab" aria-controls="xml3" aria-selected="false">
                                        <span class="tab-short-title">XML3</span>
                                        <span class="tab-full-title">Thông tin VTYT</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML4' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML4' %}active{% endif %}" id="xml4-tab" data-toggle="tab" href="#xml4" role="tab" aria-controls="xml4" aria-selected="false">
                                        <span class="tab-short-title">XML4</span>
                                        <span class="tab-full-title">Thông tin xét nghiệm</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML5' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML5' %}active{% endif %}" id="xml5-tab" data-toggle="tab" href="#xml5" role="tab" aria-controls="xml5" aria-selected="false">
                                        <span class="tab-short-title">XML5</span>
                                        <span class="tab-full-title">Diễn biến lâm sàng</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML6' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML6' %}active{% endif %}" id="xml6-tab" data-toggle="tab" href="#xml6" role="tab" aria-controls="xml6" aria-selected="false">
                                        <span class="tab-short-title">XML6</span>
                                        <span class="tab-full-title">Thông tin điều trị HIV/AIDS</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML7' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML7' %}active{% endif %}" id="xml7-tab" data-toggle="tab" href="#xml7" role="tab" aria-controls="xml7" aria-selected="false">
                                        <span class="tab-short-title">XML7</span>
                                        <span class="tab-full-title">Thông tin thai sản</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML8' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML8' %}active{% endif %}" id="xml8-tab" data-toggle="tab" href="#xml8" role="tab" aria-controls="xml8" aria-selected="false">
                                        <span class="tab-short-title">XML8</span>
                                        <span class="tab-full-title">Thông tin trẻ sơ sinh</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML9' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML9' %}active{% endif %}" id="xml9-tab" data-toggle="tab" href="#xml9" role="tab" aria-controls="xml9" aria-selected="false">
                                        <span class="tab-short-title">XML9</span>
                                        <span class="tab-full-title">Thông tin người nuôi dưỡng</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML10' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML10' %}active{% endif %}" id="xml10-tab" data-toggle="tab" href="#xml10" role="tab" aria-controls="xml10" aria-selected="false">
                                        <span class="tab-short-title">XML10</span>
                                        <span class="tab-full-title">Giấy chứng nhận nghỉ việc hưởng BHXH</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML11' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML11' %}active{% endif %}" id="xml11-tab" data-toggle="tab" href="#xml11" role="tab" aria-controls="xml11" aria-selected="false">
                                        <span class="tab-short-title">XML11</span>
                                        <span class="tab-full-title">Giấy ra viện</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML12' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML12' %}active{% endif %}" id="xml12-tab" data-toggle="tab" href="#xml12" role="tab" aria-controls="xml12" aria-selected="false">
                                        <span class="tab-short-title">XML12</span>
                                        <span class="tab-full-title">Giấy chứng nhận giám định y khoa</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML13' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML13' %}active{% endif %}" id="xml13-tab" data-toggle="tab" href="#xml13" role="tab" aria-controls="xml13" aria-selected="false">
                                        <span class="tab-short-title">XML13</span>
                                        <span class="tab-full-title">Giấy chuyển viện</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML14' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML14' %}active{% endif %}" id="xml14-tab" data-toggle="tab" href="#xml14" role="tab" aria-controls="xml14" aria-selected="false">
                                        <span class="tab-short-title">XML14</span>
                                        <span class="tab-full-title">Giấy hẹn khám lại</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if not xml_type or xml_type == 'XML15' %}
                                <li class="nav-item">
                                    <a class="nav-link {% if xml_type == 'XML15' %}active{% endif %}" id="xml15-tab" data-toggle="tab" href="#xml15" role="tab" aria-controls="xml15" aria-selected="false">
                                        <span class="tab-short-title">XML15</span>
                                        <span class="tab-full-title">Thông tin điều trị lao</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                            <div class="tab-content" id="xmlTabsContent">
                                <!-- XML0 Tab -->
                                {% if not xml_type or xml_type == 'XML0' %}
                                <div class="tab-pane fade {% if not xml_type or xml_type == 'XML0' %}show active{% endif %}" id="xml0" role="tabpanel" aria-labelledby="xml0-tab">
                                    <div id="xml0-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML1 Tab -->
                                {% if not xml_type or xml_type == 'XML1' %}
                                <div class="tab-pane fade {% if xml_type == 'XML1' %}show active{% endif %}" id="xml1" role="tabpanel" aria-labelledby="xml1-tab">
                                    <div id="xml1-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML2 Tab -->
                                {% if not xml_type or xml_type == 'XML2' %}
                                <div class="tab-pane fade {% if xml_type == 'XML2' %}show active{% endif %}" id="xml2" role="tabpanel" aria-labelledby="xml2-tab">
                                    <div id="xml2-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML3 Tab -->
                                {% if not xml_type or xml_type == 'XML3' %}
                                <div class="tab-pane fade {% if xml_type == 'XML3' %}show active{% endif %}" id="xml3" role="tabpanel" aria-labelledby="xml3-tab">
                                    <div id="xml3-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML4 Tab -->
                                {% if not xml_type or xml_type == 'XML4' %}
                                <div class="tab-pane fade {% if xml_type == 'XML4' %}show active{% endif %}" id="xml4" role="tabpanel" aria-labelledby="xml4-tab">
                                    <div id="xml4-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML5 Tab -->
                                {% if not xml_type or xml_type == 'XML5' %}
                                <div class="tab-pane fade {% if xml_type == 'XML5' %}show active{% endif %}" id="xml5" role="tabpanel" aria-labelledby="xml5-tab">
                                    <div id="xml5-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML6 Tab -->
                                {% if not xml_type or xml_type == 'XML6' %}
                                <div class="tab-pane fade {% if xml_type == 'XML6' %}show active{% endif %}" id="xml6" role="tabpanel" aria-labelledby="xml6-tab">
                                    <div id="xml6-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML7 Tab -->
                                {% if not xml_type or xml_type == 'XML7' %}
                                <div class="tab-pane fade {% if xml_type == 'XML7' %}show active{% endif %}" id="xml7" role="tabpanel" aria-labelledby="xml7-tab">
                                    <div id="xml7-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML8 Tab -->
                                {% if not xml_type or xml_type == 'XML8' %}
                                <div class="tab-pane fade {% if xml_type == 'XML8' %}show active{% endif %}" id="xml8" role="tabpanel" aria-labelledby="xml8-tab">
                                    <div id="xml8-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML9 Tab -->
                                {% if not xml_type or xml_type == 'XML9' %}
                                <div class="tab-pane fade {% if xml_type == 'XML9' %}show active{% endif %}" id="xml9" role="tabpanel" aria-labelledby="xml9-tab">
                                    <div id="xml9-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML10 Tab -->
                                {% if not xml_type or xml_type == 'XML10' %}
                                <div class="tab-pane fade {% if xml_type == 'XML10' %}show active{% endif %}" id="xml10" role="tabpanel" aria-labelledby="xml10-tab">
                                    <div id="xml10-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML11 Tab -->
                                {% if not xml_type or xml_type == 'XML11' %}
                                <div class="tab-pane fade {% if xml_type == 'XML11' %}show active{% endif %}" id="xml11" role="tabpanel" aria-labelledby="xml11-tab">
                                    <div id="xml11-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML12 Tab -->
                                {% if not xml_type or xml_type == 'XML12' %}
                                <div class="tab-pane fade {% if xml_type == 'XML12' %}show active{% endif %}" id="xml12" role="tabpanel" aria-labelledby="xml12-tab">
                                    <div id="xml12-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML13 Tab -->
                                {% if not xml_type or xml_type == 'XML13' %}
                                <div class="tab-pane fade {% if xml_type == 'XML13' %}show active{% endif %}" id="xml13" role="tabpanel" aria-labelledby="xml13-tab">
                                    <div id="xml13-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML14 Tab -->
                                {% if not xml_type or xml_type == 'XML14' %}
                                <div class="tab-pane fade {% if xml_type == 'XML14' %}show active{% endif %}" id="xml14" role="tabpanel" aria-labelledby="xml14-tab">
                                    <div id="xml14-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}

                                <!-- XML15 Tab -->
                                {% if not xml_type or xml_type == 'XML15' %}
                                <div class="tab-pane fade {% if xml_type == 'XML15' %}show active{% endif %}" id="xml15" role="tabpanel" aria-labelledby="xml15-tab">
                                    <div id="xml15-table" class="xml-tabulator" style="position: relative;"></div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'AdminLTE-3.0.1/plugins/bs-custom-file-input/bs-custom-file-input.min.js' %}"></script>
<script src="https://unpkg.com/tabulator-tables@6.3.1/dist/js/tabulator.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/luxon@3.4.4/build/global/luxon.min.js"></script>
<script>
    // Định nghĩa URL API để sử dụng trong các file JS
    var API_CATEGORY_DATA_URL = "{% url 'danhmuc130:api_category_data' %}";
</script>
<script src="{% static 'js/xml4750/tabulator_xml_func.js' %}"></script>
<script src="{% static 'js/xml4750/tabulator_xml_tables.js' %}"></script>
<script>
    $(function () {
        // Định nghĩa các định dạng cho từng danh mục
        var categoryFormats = {
            'doituongkcb': 'ma_diengiai',  // Mã + diễn giải cho đối tượng KCB
            'quoctich': 'ma_ten',          // Mã + tên cho các danh mục khác
            'nghenghiep': 'ma_ten',
            'matainan': 'ma_ten',
            'ketquadieutri': 'ma_ten',
            'loairavien': 'ma_ten'
        };

        // Khởi tạo custom-file-input
        bsCustomFileInput.init();

        // Chuẩn bị dữ liệu Django để truyền vào hàm setupTables
        var djangoData = {
            {% if xml0_list %}
            xml0_list: [
                {% for item in xml0_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    maBN: "{{ item.maBN|default:'' }}",
                    hoTen: "{{ item.hoTen|default:'' }}",
                    soCCCD: "{{ item.soCCCD|default:'' }}",
                    ngaySinh: "{{ item.ngaySinh|default:'' }}",
                    gioiTinh: "{{ item.gioiTinh|default:'' }}",
                    maTheBHYT: "{{ item.maTheBHYT|default:'' }}",
                    maDKBD: "{{ item.maDKBD|default:'' }}",
                    gtTheTu: "{{ item.gtTheTu|default:'' }}",
                    gtTheDen: "{{ item.gtTheDen|default:'' }}",
                    maDoiTuongKCB: "{{ item.maDoiTuongKCB|default:'' }}",
                    ngayVao: "{{item.ngayVao|default:'' }}",
                    ngayVaoNoiTru: "{{item.ngayVaoNoiTru|default:'' }}",
                    ly_do_vnt: "{{ item.ly_do_vnt|default:'' }}",
                    ma_ly_do_vnt: "{{ item.ma_ly_do_vnt|default:'' }}",
                    maLoaiKCB: "{{ item.maLoaiKCB|default:'' }}",
                    maCSKCB: "{{ item.maCSKCB|default:'' }}",
                    maDichVu: "{{ item.maDichVu|default:'' }}",
                    tenDichVu: "{{ item.tenDichVu|default:'' }}",
                    ma_thuoc: "{{ item.ma_thuoc|default:'' }}",
                    tenThuoc: "{{ item.tenThuoc|default:'' }}",
                    maVatTu: "{{ item.maVatTu|default:'' }}",
                    tenVatTu: "{{ item.tenVatTu|default:'' }}",
                    ngayYL: "{{item.ngayYL|default:'' }}",
                    duPhong: "{{ item.duPhong|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% if xml0_pagination %}
            xml0_pagination: {
                total: {{ xml0_pagination.total }},
                per_page: {{ xml0_pagination.per_page }},
                current_page: {{ xml0_pagination.current_page }},
                last_page: {{ xml0_pagination.last_page }},
                has_next: {{ xml0_pagination.has_next|lower }},
                has_previous: {{ xml0_pagination.has_previous|lower }}
            },
            {% endif %}
            {% endif %}

            {% if xml1_list %}
            xml1_list: [
                {% for item in xml1_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    maBN: "{{ item.maBN|default:'' }}",
                    hoTen: "{{ item.hoTen|default:'' }}",
                    soCCCD: "{{ item.soCCCD|default:'' }}",
                    ngaySinh: "{{ item.ngaySinh|default:'' }}",
                    gioiTinh: "{{ item.gioiTinh|default:'' }}",
                    nhomMau: "{{ item.nhomMau|default:'' }}",
                    maQuocTich: "{{ item.maQuocTich|default:'' }}",
                    maDanToc: "{{ item.maDanToc|default:'' }}",
                    maNgheNghiep: "{{ item.maNgheNghiep|default:'' }}",
                    diaChi: "{{ item.diaChi|default:'' }}",
                    maTinhCuTru: "{{ item.maTinhCuTru|default:'' }}",
                    maHuyenCuTru: "{{ item.maHuyenCuTru|default:'' }}",
                    maXaCuTru: "{{ item.maXaCuTru|default:'' }}",
                    soDienThoai: "{{ item.soDienThoai|default:'' }}",
                    maThe: "{{ item.maTheBHYT|default:'' }}",
                    maDKBD: "{{ item.maDKBD|default:'' }}",
                    gtTheTu: "{{ item.gtTheTu|default:'' }}",
                    gtTheDen: "{{ item.gtTheDen|default:'' }}",
                    ngayMienCCT: "{{ item.ngayMienCCT|default:'' }}",
                    lyDoVaoVien: "{{ item.lyDoVaoVien|default:'' }}",
                    lyDoVaoNoiTru: "{{ item.lyDoVaoNoiTru|default:'' }}",
                    maLyDoVaoNoiTru: "{{ item.maLyDoVaoNoiTru|default:'' }}",
                    chanDoanVao: "{{ item.chanDoanVao|default:'' }}",
                    chanDoanRaVien: "{{ item.chanDoanRaVien|default:'' }}",
                    maBenhChinh: "{{ item.maBenhChinh|default:'' }}",
                    maBenhKem1: "{{ item.maBenhKem1|default:'' }}",
                    maBenhKem2: "{{ item.maBenhKem2|default:'' }}",
                    maBenhKem3: "{{ item.maBenhKem3|default:'' }}",
                    ngayVao: "{{ item.ngayVao|default:'' }}",
                    ngayRa: "{{ item.ngayRa|default:'' }}",
                    soNgayDieuTri: "{{ item.soNgayDieuTri|default:'' }}",
                    kqDieuTri: "{{ item.kqDieuTri|default:'' }}",
                    tinhTrangRaVien: "{{ item.tinhTrangRaVien|default:'' }}",
                    ngayTuVong: "{{ item.ngayTuVong|default:'' }}",
                    maNguyenNhanTuVong: "{{ item.maNguyenNhanTuVong|default:'' }}",
                    soNgayVCC: "{{ item.soNgayVCC|default:'' }}",
                    tienThuoc: "{{ item.tienThuoc|default:'' }}",
                    tienMau: "{{ item.tienMau|default:'' }}",
                    tienDichTruyen: "{{ item.tienDichTruyen|default:'' }}",
                    tienVatTuYTe: "{{ item.tienVatTuYTe|default:'' }}",
                    tienXetNghiem: "{{ item.tienXetNghiem|default:'' }}",
                    tienChanDoanHA: "{{ item.tienChanDoanHA|default:'' }}",
                    tienThuThuat: "{{ item.tienThuThuat|default:'' }}",
                    tienPhauThuat: "{{ item.tienPhauThuat|default:'' }}",
                    tienKhamBenh: "{{ item.tienKhamBenh|default:'' }}",
                    tienGiuongBenh: "{{ item.tienGiuongBenh|default:'' }}",
                    tienVanChuyen: "{{ item.tienVanChuyen|default:'' }}",
                    tienKhac: "{{ item.tienKhac|default:'' }}",
                    tienBNTra: "{{ item.tienBNTra|default:'' }}",
                    tienBHYTTra: "{{ item.tienBHYTTra|default:'' }}",
                    tienNgoaiDS: "{{ item.tienNgoaiDS|default:'' }}",
                    tienNguonKhac: "{{ item.tienNguonKhac|default:'' }}",
                    tienCungTra: "{{ item.tienCungTra|default:'' }}",
                    tienTamUng: "{{ item.tienTamUng|default:'' }}",
                    tienThanhToan: "{{ item.tienThanhToan|default:'' }}",
                    ngayThanhToan: "{{ item.ngayThanhToan|default:'' }}",
                    maTaiNan: "{{ item.maTaiNan|default:'' }}",
                    ngayTaiNan: "{{ item.ngayTaiNan|default:'' }}",
                    soHSBA: "{{ item.soHSBA|default:'' }}",
                    maTTDV: "{{ item.maTTDV|default:'' }}",
                    duPhong: "{{ item.duPhong|default:'' }}",
                    maDoiTuongKCB: "{{ item.maDoiTuongKCB|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% if xml1_pagination %}
            xml1_pagination: {
                total: {{ xml1_pagination.total }},
                per_page: {{ xml1_pagination.per_page }},
                current_page: {{ xml1_pagination.current_page }},
                last_page: {{ xml1_pagination.last_page }},
                has_next: {{ xml1_pagination.has_next|lower }},
                has_previous: {{ xml1_pagination.has_previous|lower }}
            },
            {% endif %}
            {% endif %}

            {% if xml2_list %}
            xml2_list: [
                {% for item in xml2_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    stt: "{{ item.stt|default:'' }}",
                    maThuoc: "{{ item.maThuoc|default:'' }}",
                    maNhom: "{{ item.maNhom|default:'' }}",
                    tenThuoc: "{{ item.tenThuoc|default:'' }}",
                    donViTinh: "{{ item.donViTinh|default:'' }}",
                    hamLuong: "{{ item.hamLuong|default:'' }}",
                    duongDung: "{{ item.duongDung|default:'' }}",
                    lieuDung: "{{ item.lieuDung|default:'' }}",
                    soLuong: "{{ item.soLuong|default:'' }}",
                    donGia: "{{ item.donGia|default:'' }}",
                    thanhTien: "{{ item.thanhTien|default:'' }}",
                    maKhoa: "{{ item.maKhoa|default:'' }}",
                    tenKhoa: "{{ item.tenKhoa|default:'' }}",
                    maBacSi: "{{ item.maBacSi|default:'' }}",
                    tenBacSi: "{{ item.tenBacSi|default:'' }}",
                    ngayYLenh: "{{ item.ngayYLenh|default:'' }}",
                    ngayDung: "{{ item.ngayDung|default:'' }}",
                    ghiChu: "{{ item.ghiChu|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% if xml2_pagination %}
            xml2_pagination: {
                total: {{ xml2_pagination.total }},
                per_page: {{ xml2_pagination.per_page }},
                current_page: {{ xml2_pagination.current_page }},
                last_page: {{ xml2_pagination.last_page }},
                has_next: {{ xml2_pagination.has_next|lower }},
                has_previous: {{ xml2_pagination.has_previous|lower }}
            },
            {% endif %}
            {% endif %}

            {% if xml3_list %}
            xml3_list: [
                {% for item in xml3_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    stt: "{{ item.stt|default:'' }}",
                    maVatTu: "{{ item.maVatTu|default:'' }}",
                    maNhom: "{{ item.maNhom|default:'' }}",
                    tenVatTu: "{{ item.tenVatTu|default:'' }}",
                    donViTinh: "{{ item.donViTinh|default:'' }}",
                    phamVi: "{{ item.phamVi|default:'' }}",
                    soLuong: "{{ item.soLuong|default:'' }}",
                    donGiaBV: "{{ item.donGiaBV|default:'' }}",
                    donGiaBH: "{{ item.donGiaBH|default:'' }}",
                    tyLeBH: "{{ item.tyLeBH|default:'' }}",
                    thanhTienBV: "{{ item.thanhTienBV|default:'' }}",
                    thanhTienBH: "{{ item.thanhTienBH|default:'' }}",
                    maKhoa: "{{ item.maKhoa|default:'' }}",
                    tenKhoa: "{{ item.tenKhoa|default:'' }}",
                    maBacSi: "{{ item.maBacSi|default:'' }}",
                    tenBacSi: "{{ item.tenBacSi|default:'' }}",
                    ngayYLenh: "{{ item.ngayYLenh|default:'' }}",
                    ngayDung: "{{ item.ngayDung|default:'' }}",
                    ghiChu: "{{ item.ghiChu|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% if xml3_pagination %}
            xml3_pagination: {
                total: {{ xml3_pagination.total }},
                per_page: {{ xml3_pagination.per_page }},
                current_page: {{ xml3_pagination.current_page }},
                last_page: {{ xml3_pagination.last_page }},
                has_next: {{ xml3_pagination.has_next|lower }},
                has_previous: {{ xml3_pagination.has_previous|lower }}
            },
            {% endif %}
            {% endif %}

            {% if xml4_list %}
            xml4_list: [
                {% for item in xml4_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    stt: "{{ item.stt|default:'' }}",
                    maDichVu: "{{ item.maDichVu|default:'' }}",
                    maChiSo: "{{ item.maChiSo|default:'' }}",
                    tenChiSo: "{{ item.tenChiSo|default:'' }}",
                    giaTri: "{{ item.giaTri|default:'' }}",
                    maMay: "{{ item.maMay|default:'' }}",
                    moTa: "{{ item.moTa|default:'' }}",
                    ketLuan: "{{ item.ketLuan|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% if xml4_pagination %}
            xml4_pagination: {
                total: {{ xml4_pagination.total }},
                per_page: {{ xml4_pagination.per_page }},
                current_page: {{ xml4_pagination.current_page }},
                last_page: {{ xml4_pagination.last_page }},
                has_next: {{ xml4_pagination.has_next|lower }},
                has_previous: {{ xml4_pagination.has_previous|lower }}
            },
            {% endif %}
            {% endif %}

            {% if xml5_list %}
            xml5_list: [
                {% for item in xml5_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    stt: "{{ item.stt|default:'' }}",
                    ngayKham: "{{ item.ngayKham|default:'' }}",
                    maBacSi: "{{ item.maBacSi|default:'' }}",
                    tenBacSi: "{{ item.tenBacSi|default:'' }}",
                    maKhoa: "{{ item.maKhoa|default:'' }}",
                    tenKhoa: "{{ item.tenKhoa|default:'' }}",
                    dienBien: "{{ item.dienBien|default:'' }}",
                    hoiChan: "{{ item.hoiChan|default:'' }}",
                    phauThuat: "{{ item.phauThuat|default:'' }}",
                    ghiChu: "{{ item.ghiChu|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% if xml5_pagination %}
            xml5_pagination: {
                total: {{ xml5_pagination.total }},
                per_page: {{ xml5_pagination.per_page }},
                current_page: {{ xml5_pagination.current_page }},
                last_page: {{ xml5_pagination.last_page }},
                has_next: {{ xml5_pagination.has_next|lower }},
                has_previous: {{ xml5_pagination.has_previous|lower }}
            },
            {% endif %}
            {% endif %}

            {% if xml6_list %}
            xml6_list: [
                {% for item in xml6_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    maTheBHYT: "{{ item.maTheBHYT|default:'' }}",
                    soCCCD: "{{ item.soCCCD|default:'' }}",
                    ngaySinh: "{{ item.ngaySinh|default:'' }}",
                    maTinhCuTru: "{{ item.maTinhCuTru|default:'' }}",
                    maHuyenCuTru: "{{ item.maHuyenCuTru|default:'' }}",
                    maXaCuTru: "{{ item.maXaCuTru|default:'' }}",
                    ngayKDHIV: "{{ item.ngayKDHIV|default:'' }}",
                    noiLayMauXN: "{{ item.noiLayMauXN|default:'' }}",
                    noiXNKD: "{{ item.noiXNKD|default:'' }}",
                    noiBDDTARV: "{{ item.noiBDDTARV|default:'' }}",
                    maPhaDoDieuTriBD: "{{ item.maPhaDoDieuTriBD|default:'' }}",
                    loaiDtriLao: "{{ item.loaiDtriLao|default:'' }}",
                    phacDoDtriLao: "{{ item.phacDoDtriLao|default:'' }}",
                    kqDTLao: "{{ item.kqDTLao|default:'' }}",
                    ghiChu: "{{ item.ghiChu|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% endif %}

            {% if xml7_list %}
            xml7_list: [
                {% for item in xml7_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    soLuuTru: "{{ item.soLuuTru|default:'' }}",
                    maYTe: "{{ item.maYTe|default:'' }}",
                    maKhoaRV: "{{ item.maKhoaRV|default:'' }}",
                    ngayVao: "{{ item.ngayVao|default:'' }}",
                    ngayRa: "{{ item.ngayRa|default:'' }}",
                    maDinhChiThai: "{{ item.maDinhChiThai|default:'' }}",
                    chanDoanRV: "{{ item.chanDoanRV|default:'' }}",
                    ppDieuTri: "{{ item.ppDieuTri|default:'' }}",
                    tuoiThai: "{{ item.tuoiThai|default:'' }}",
                    nguyenNhanDinhChi: "{{ item.nguyenNhanDinhChi|default:'' }}",
                    maCha: "{{ item.maCha|default:'' }}",
                    maMe: "{{ item.maMe|default:'' }}",
                    hoTenCha: "{{ item.hoTenCha|default:'' }}",
                    hoTenMe: "{{ item.hoTenMe|default:'' }}",
                    ghiChu: "{{ item.ghiChu|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% endif %}

            {% if xml8_list %}
            xml8_list: [
                {% for item in xml8_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    hoTenCha: "{{ item.hoTenCha|default:'' }}",
                    hoTenMe: "{{ item.hoTenMe|default:'' }}",
                    nguoiGiamHo: "{{ item.nguoiGiamHo|default:'' }}",
                    ngayVao: "{{ item.ngayVao|default:'' }}",
                    ngayRa: "{{ item.ngayRa|default:'' }}",
                    chanDoanVao: "{{ item.chanDoanVao|default:'' }}",
                    chanDoanRV: "{{ item.chanDoanRV|default:'' }}",
                    qtBenhLy: "{{ item.qtBenhLy|default:'' }}",
                    tomTatKQ: "{{ item.tomTatKQ|default:'' }}",
                    ngaySinhCon: "{{ item.ngaySinhCon|default:'' }}",
                    ngayConChet: "{{ item.ngayConChet|default:'' }}",
                    soConChet: "{{ item.soConChet|default:'' }}",
                    ppDieuTri: "{{ item.ppDieuTri|default:'' }}",
                    ketQuaDtri: "{{ item.ketQuaDtri|default:'' }}",
                    ghiChu: "{{ item.ghiChu|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% endif %}

            {% if xml9_list %}
            xml9_list: [
                {% for item in xml9_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    hotenNND: "{{ item.hotenNND|default:'' }}",
                    ngaySinhNND: "{{ item.ngaySinhNND|default:'' }}",
                    soCCCDNND: "{{ item.soCCCDNND|default:'' }}",
                    noiCuTruNND: "{{ item.noiCuTruNND|default:'' }}",
                    maTinhCuTru: "{{ item.maTinhCuTru|default:'' }}",
                    maHuyenCuTru: "{{ item.maHuyenCuTru|default:'' }}",
                    maXaCuTru: "{{ item.maXaCuTru|default:'' }}",
                    hoTenCon: "{{ item.hoTenCon|default:'' }}",
                    gioiTinhCon: "{{ item.gioiTinhCon|default:'' }}",
                    soCon: "{{ item.soCon|default:'' }}",
                    lanSinh: "{{ item.lanSinh|default:'' }}",
                    canNangCon: "{{ item.canNangCon|default:'' }}",
                    ngaySinhCon: "{{ item.ngaySinhCon|default:'' }}",
                    noiSinhCon: "{{ item.noiSinhCon|default:'' }}",
                    tinhTrangCon: "{{ item.tinhTrangCon|default:'' }}",
                    sinhConPhauThuat: "{{ item.sinhConPhauThuat|default:'' }}",
                    nguoiDoDe: "{{ item.nguoiDoDe|default:'' }}",
                    ghiChu: "{{ item.ghiChu|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% endif %}

            {% if xml10_list %}
            xml10_list: [
                {% for item in xml10_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    soSeri: "{{ item.soSeri|default:'' }}",
                    soCT: "{{ item.soCT|default:'' }}",
                    soNgay: "{{ item.soNgay|default:'' }}",
                    donVi: "{{ item.donVi|default:'' }}",
                    chanDoanRV: "{{ item.chanDoanRV|default:'' }}",
                    tuNgay: "{{ item.tuNgay|default:'' }}",
                    denNgay: "{{ item.denNgay|default:'' }}",
                    maBS: "{{ item.maBS|default:'' }}",
                    tenBS: "{{ item.tenBS|default:'' }}",
                    ngayCT: "{{ item.ngayCT|default:'' }}",
                    ghiChu: "{{ item.ghiChu|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% endif %}

            {% if xml11_list %}
            xml11_list: [
                {% for item in xml11_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    soCT: "{{ item.soCT|default:'' }}",
                    soSeri: "{{ item.soSeri|default:'' }}",
                    soKCB: "{{ item.soKCB|default:'' }}",
                    donVi: "{{ item.donVi|default:'' }}",
                    maBHXH: "{{ item.maBHXH|default:'' }}",
                    maTheBHYT: "{{ item.maTheBHYT|default:'' }}",
                    chanDoanRV: "{{ item.chanDoanRV|default:'' }}",
                    ppDieuTri: "{{ item.ppDieuTri|default:'' }}",
                    maDinhChiThai: "{{ item.maDinhChiThai|default:'' }}",
                    tuoiThai: "{{ item.tuoiThai|default:'' }}",
                    nguyenNhanDinhChi: "{{ item.nguyenNhanDinhChi|default:'' }}",
                    soNgayNghi: "{{ item.soNgayNghi|default:'' }}",
                    tuNgay: "{{ item.tuNgay|default:'' }}",
                    denNgay: "{{ item.denNgay|default:'' }}",
                    ghiChu: "{{ item.ghiChu|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% endif %}

            {% if xml12_list %}
            xml12_list: [
                {% for item in xml12_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    nguoiChuTri: "{{ item.nguoiChuTri|default:'' }}",
                    chucVu: "{{ item.chucVu|default:'' }}",
                    ngayHop: "{{ item.ngayHop|default:'' }}",
                    hoTen: "{{ item.hoTen|default:'' }}",
                    ngaySinh: "{{ item.ngaySinh|default:'' }}",
                    soCCCD: "{{ item.soCCCD|default:'' }}",
                    diaChi: "{{ item.diaChi|default:'' }}",
                    maTinhCuTru: "{{ item.maTinhCuTru|default:'' }}",
                    maHuyenCuTru: "{{ item.maHuyenCuTru|default:'' }}",
                    maXaCuTru: "{{ item.maXaCuTru|default:'' }}",
                    maBHXH: "{{ item.maBHXH|default:'' }}",
                    maTheBHYT: "{{ item.maTheBHYT|default:'' }}",
                    tyLeTTCTCu: "{{ item.tyLeTTCTCu|default:'' }}",
                    tyLeTTCTMoi: "{{ item.tyLeTTCTMoi|default:'' }}",
                    tongTyLeTTCT: "{{ item.tongTyLeTTCT|default:'' }}",
                    ghiChu: "{{ item.ghiChu|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% endif %}

            {% if xml13_list %}
            xml13_list: [
                {% for item in xml13_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    soHoSo: "{{ item.soHoSo|default:'' }}",
                    soChuyenTuyen: "{{ item.soChuyenTuyen|default:'' }}",
                    giayChuyenTuyen: "{{ item.giayChuyenTuyen|default:'' }}",
                    maCSKCB: "{{ item.maCSKCB|default:'' }}",
                    maNoiDi: "{{ item.maNoiDi|default:'' }}",
                    maNoiDen: "{{ item.maNoiDen|default:'' }}",
                    hoTen: "{{ item.hoTen|default:'' }}",
                    ngaySinh: "{{ item.ngaySinh|default:'' }}",
                    gioiTinh: "{{ item.gioiTinh|default:'' }}",
                    maTheBHYT: "{{ item.maTheBHYT|default:'' }}",
                    gtTheDen: "{{ item.gtTheDen|default:'' }}",
                    chanDoanRV: "{{ item.chanDoanRV|default:'' }}",
                    qtBenhLy: "{{ item.qtBenhLy|default:'' }}",
                    huongDieuTri: "{{ item.huongDieuTri|default:'' }}",
                    ghiChu: "{{ item.ghiChu|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% endif %}

            {% if xml14_list %}
            xml14_list: [
                {% for item in xml14_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    soGiayHenKL: "{{ item.soGiayHenKL|default:'' }}",
                    maCSKCB: "{{ item.maCSKCB|default:'' }}",
                    hoTen: "{{ item.hoTen|default:'' }}",
                    ngaySinh: "{{ item.ngaySinh|default:'' }}",
                    gioiTinh: "{{ item.gioiTinh|default:'' }}",
                    diaChi: "{{ item.diaChi|default:'' }}",
                    maTheBHYT: "{{ item.maTheBHYT|default:'' }}",
                    gtTheDen: "{{ item.gtTheDen|default:'' }}",
                    ngayVao: "{{ item.ngayVao|default:'' }}",
                    ngayRa: "{{ item.ngayRa|default:'' }}",
                    ngayHenKL: "{{ item.ngayHenKL|default:'' }}",
                    chanDoanRV: "{{ item.chanDoanRV|default:'' }}",
                    maBenhChinh: "{{ item.maBenhChinh|default:'' }}",
                    maBenhKT: "{{ item.maBenhKT|default:'' }}",
                    ghiChu: "{{ item.ghiChu|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% endif %}

            {% if xml15_list %}
            xml15_list: [
                {% for item in xml15_list %}
                {
                    id: "{{ item.id }}",
                    maLK: "{{ item.maLK|default:'' }}",
                    maBN: "{{ item.maBN|default:'' }}",
                    hoTen: "{{ item.hoTen|default:'' }}",
                    soCCCD: "{{ item.soCCCD|default:'' }}",
                    phanLoaiLaoViTri: "{{ item.phanLoaiLaoViTri|default:'' }}",
                    phanLoaiLaoTS: "{{ item.phanLoaiLaoTS|default:'' }}",
                    phanLoaiLaoHIV: "{{ item.phanLoaiLaoHIV|default:'' }}",
                    LoaiDTriLao: "{{ item.LoaiDTriLao|default:'' }}",
                    phacDoDTriLao: "{{ item.phacDoDTriLao|default:'' }}",
                    ngayBDDTriLao: "{{ item.ngayBDDTriLao|default:'' }}",
                    ngayKTDTriLao: "{{ item.ngayKTDTriLao|default:'' }}",
                    ketQuaDTriLao: "{{ item.ketQuaDTriLao|default:'' }}",
                    ghiChu: "{{ item.ghiChu|default:'' }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            {% endif %}
        };
        // Load dữ liệu danh mục và thiết lập bảng
        loadCategoryData(['doituongkcb', 'quoctich', 'nghenghiep', 'matainan', 'ketquadieutri', 'loairavien'], categoryFormats).done(function() {
            console.log("Tất cả dữ liệu danh mục đã được load xong!");

            // Thêm CSS để ẩn các bảng Tabulator trong tab không hiển thị
            $("<style>")
                .prop("type", "text/css")
                .html(`
                    .tab-pane:not(.active) .tabulator {
                        display: none !important;
                    }
                `)
                .appendTo("head");

            // Gọi hàm thiết lập bảng khi trang được tải
            setupTables(djangoData);

            // Xử lý khi chuyển tab
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var target = $(e.target).attr("href");
                var targetTab = $(target);
                var tabId = targetTab.attr('id');

                // Đảm bảo tab hiển thị đúng
                targetTab.css('display', 'block');

                // Thiết lập lại bảng cho tab mới
                setupTables(djangoData);

                // Đảm bảo rằng chỉ có một bảng Tabulator trong tab
                console.log("Tab switched to: " + tabId);

                // Kích hoạt sự kiện resize để Tabulator điều chỉnh kích thước
                $(window).trigger('resize');
            });
        }); // Đóng hàm .done()

        // Xử lý khi thay đổi kích thước cửa sổ
        $(window).resize(function() {
            // Tính toán chiều cao cho bảng
            var windowHeight = $(window).height();
            var headerHeight = $('.main-header').outerHeight() || 0;
            var contentHeaderHeight = $('.content-header').outerHeight() || 0;
            var cardHeaderHeight = $('.card-header').outerHeight() || 0;
            var tabsHeight = $('#xmlTabs').outerHeight() || 0;
            var footerHeight = $('.main-footer').outerHeight() || 0;
            var controlsHeight = 100;

            var availableHeight = windowHeight - headerHeight - contentHeaderHeight - cardHeaderHeight - tabsHeight - footerHeight - controlsHeight;
            availableHeight = Math.max(availableHeight, 300);

            // Cập nhật chiều cao cho Tabulator
            $('.xml-tabulator').css('height', availableHeight + 'px');

            // Cập nhật lại kích thước của Tabulator
            $('.tab-pane.active .xml-tabulator').each(function() {
                var tabulator = $(this)[0]._tabulator;
                if (tabulator) {
                    tabulator.redraw(true);
                }
            });
        });

        // Xử lý sự kiện xóa XML
        $(document).on('click', '.delete-xml', function() {
            var id = $(this).data('id');
            var type = $(this).data('type');

            Swal.fire({
                title: 'Xác nhận xóa',
                text: "Bạn có chắc chắn muốn xóa dữ liệu này?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Xóa',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Gửi yêu cầu xóa
                    $.ajax({
                        url: '/xml4750/delete/' + type + '/' + id + '/',
                        type: 'POST',
                        data: {
                            'csrfmiddlewaretoken': '{{ csrf_token }}'
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire(
                                    'Đã xóa!',
                                    'Dữ liệu đã được xóa thành công.',
                                    'success'
                                ).then(() => {
                                    // Tải lại trang
                                    window.location.reload();
                                });
                            } else {
                                Swal.fire(
                                    'Lỗi!',
                                    response.message || 'Đã xảy ra lỗi khi xóa dữ liệu.',
                                    'error'
                                );
                            }
                        },
                        error: function() {
                            Swal.fire(
                                'Lỗi!',
                                'Đã xảy ra lỗi khi xóa dữ liệu.',
                                'error'
                            );
                        }
                    });
                }
            });
        });

        // Xử lý sự kiện xuất XML
        $(document).on('click', '.export-xml', function() {
            var id = $(this).data('id');
            var type = $(this).data('type');

            // Chuyển hướng đến trang xuất XML
            window.location.href = '/xml4750/export/' + type + '/' + id + '/';
        });

        // Form validation for import XML
        $('#import-xml-form').submit(function(e) {
            var xmlType = $('#xml_type').val();
            var xmlFile = $('#xml_file').val();

            if (!xmlType || !xmlFile) {
                e.preventDefault();
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi',
                    text: 'Vui lòng chọn loại XML và file XML!',
                });
            }
        });

        // Form validation for export XML
        $('#export-xml-form').submit(function(e) {
            var xmlType = $('#export_xml_type').val();
            var maLK = $('#export_maLK').val();

            if (!xmlType || !maLK) {
                e.preventDefault();
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi',
                    text: 'Vui lòng chọn loại XML và nhập mã liên kết!',
                });
            }
        });

        // Xử lý khi nhấn nút "Xóa tất cả"
        $(document).on('click', '.delete-all-btn', function() {
            var xmlType = $(this).data('type');

            Swal.fire({
                title: 'Xác nhận xóa tất cả',
                text: "Bạn có chắc chắn muốn xóa tất cả dữ liệu " + xmlType + "?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Xóa tất cả',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Lấy CSRF token từ cookie
                    function getCookie(name) {
                        let cookieValue = null;
                        if (document.cookie && document.cookie !== '') {
                            const cookies = document.cookie.split(';');
                            for (let i = 0; i < cookies.length; i++) {
                                const cookie = cookies[i].trim();
                                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                                    break;
                                }
                            }
                        }
                        return cookieValue;
                    }

                    const csrftoken = getCookie('csrftoken');

                    // Hiển thị thông báo đang xử lý
                    Swal.fire({
                        title: 'Đang xử lý',
                        html: 'Vui lòng đợi trong khi hệ thống xóa dữ liệu...',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Gửi yêu cầu xóa tất cả
                    $.ajax({
                        url: "/xml4750/delete_all/" + xmlType + "/",
                        type: "POST",
                        data: {},
                        headers: {
                            'X-CSRFToken': csrftoken
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire(
                                    'Đã xóa!',
                                    'Tất cả dữ liệu ' + xmlType + ' đã được xóa thành công.',
                                    'success'
                                ).then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire(
                                    'Lỗi!',
                                    response.message || 'Đã xảy ra lỗi khi xóa dữ liệu.',
                                    'error'
                                );
                            }
                        },
                        error: function(xhr, status, error) {
                            Swal.fire(
                                'Lỗi!',
                                'Đã xảy ra lỗi khi xóa dữ liệu: ' + error,
                                'error'
                            );
                        }
                    });
                }
            });
        });

        // Xử lý khi nhấn nút "Xóa đã chọn"
        $(document).on('click', '.delete-selected-btn', function() {
            var xmlType = $(this).data('type');
            var selectedRows = [];

            // Lấy danh sách các hàng đã chọn từ Tabulator
            var activeTabId = $('.tab-pane.active').attr('id');
            var tableElement = document.getElementById(activeTabId + '-table');

            if (tableElement && tableElement._tabulator) {
                var tabulator = tableElement._tabulator;
                var selectedData = tabulator.getSelectedData();

                if (selectedData.length === 0) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Cảnh báo',
                        text: 'Vui lòng chọn ít nhất một dòng để xóa'
                    });
                    return;
                }

                // Lấy ID của các hàng đã chọn
                for (var i = 0; i < selectedData.length; i++) {
                    selectedRows.push(selectedData[i].id);
                }

                // Lấy CSRF token từ cookie
                function getCookie(name) {
                    let cookieValue = null;
                    if (document.cookie && document.cookie !== '') {
                        const cookies = document.cookie.split(';');
                        for (let i = 0; i < cookies.length; i++) {
                            const cookie = cookies[i].trim();
                            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                                break;
                            }
                        }
                    }
                    return cookieValue;
                }

                const csrftoken = getCookie('csrftoken');

                Swal.fire({
                    title: 'Xác nhận xóa đã chọn',
                    text: "Bạn có chắc chắn muốn xóa " + selectedRows.length + " dòng đã chọn?",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Xóa',
                    cancelButtonText: 'Hủy'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Hiển thị thông báo đang xử lý
                        Swal.fire({
                            title: 'Đang xử lý',
                            html: 'Vui lòng đợi trong khi hệ thống xóa dữ liệu...',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        // Gửi yêu cầu xóa các dòng đã chọn
                        $.ajax({
                            url: "/xml4750/delete_selected/" + xmlType + "/",
                            type: "POST",
                            data: {
                                'selected_rows': JSON.stringify(selectedRows)
                            },
                            headers: {
                                'X-CSRFToken': csrftoken
                            },
                            success: function(response) {
                                if (response.success) {
                                    Swal.fire(
                                        'Đã xóa!',
                                        'Các dòng đã chọn đã được xóa thành công.',
                                        'success'
                                    ).then(() => {
                                        location.reload();
                                    });
                                } else {
                                    Swal.fire(
                                        'Lỗi!',
                                        response.message || 'Đã xảy ra lỗi khi xóa dữ liệu.',
                                        'error'
                                    );
                                }
                            },
                            error: function(xhr, status, error) {
                                Swal.fire(
                                    'Lỗi!',
                                    'Đã xảy ra lỗi khi xóa dữ liệu: ' + error,
                                    'error'
                                );
                            }
                        });
                    }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi',
                    text: 'Không thể tìm thấy bảng dữ liệu'
                });
            }
        });
    });
</script>
{% endblock %}
