from django.db import models

# Create your models here.

class XML0Model(models.Model):
    """
    Bảng 0 - Chỉ tiêu dữ liệu về trạng thái kh<PERSON> bệnh, ch<PERSON><PERSON> b<PERSON><PERSON> (Bảng check-in)
    """
    maLK = models.CharField(max_length=100, verbose_name="Mã liên kết")
    stt = models.IntegerField(verbose_name="Số thứ tự")
    maBN = models.CharField(max_length=100, verbose_name="Mã bệnh nhân")
    hoTen = models.CharField(max_length=255, verbose_name="Họ và tên bệnh nhân")
    soCCCD = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Số CCCD")
    ngaySinh = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày sinh")
    gioiTinh = models.IntegerField(blank=True, null=True, verbose_name="G<PERSON>ớ<PERSON> tính")
    maTheBHYT = models.CharField(max_length=15, blank=True, null=True, verbose_name="Mã thẻ BHYT")
    maDKBD = models.CharField(max_length=5, blank=True, null=True, verbose_name="Mã đăng ký KCB ban đầu")
    gtTheTu = models.CharField(max_length=10, blank=True, null=True, verbose_name="Giá trị thẻ từ")
    gtTheDen = models.CharField(max_length=10, blank=True, null=True, verbose_name="Giá trị thẻ đến")
    maDoiTuongKCB = models.CharField(max_length=4, blank=True, null=True, verbose_name="Mã đối tượng KCB")
    ngayVao = models.CharField(max_length=12, verbose_name="Ngày vào")
    ngayVaoNoiTru = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày vào nội trú")
    ly_do_vnt = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Lý do vào nội trú")
    ma_ly_do_vnt = models.CharField(max_length=5, blank=True, null=True, verbose_name="Mã lý do vào nội trú")
    maLoaiKCB = models.CharField(max_length=2, blank=True, null=True, verbose_name="Mã loại KCB")
    maCSKCB = models.CharField(max_length=5, blank=True, null=True, verbose_name="Mã cơ sở KCB")
    maDichVu = models.CharField(max_length=50, blank=True, null=True, verbose_name="Mã dịch vụ")
    tenDichVu = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Tên dịch vụ")
    ma_thuoc = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã thuốc")
    tenThuoc = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Tên thuốc")
    maVatTu = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã vật tư")
    tenVatTu = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Tên vật tư")
    ngayYL = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày y lệnh")
    duPhong = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Bảng check-in"
        verbose_name_plural = "Bảng check-in"
        db_table = "xml0_model"

    def __str__(self):
        return f"{self.maLK} - {self.hoTen}"


class XML1Model(models.Model):
    """
    XML1Model - Thông tin hành chính của bệnh nhân
    """
    maLK = models.CharField(max_length=100, verbose_name="Mã liên kết")
    maBN = models.CharField(max_length=100, verbose_name="Mã bệnh nhân")
    hoTen = models.CharField(max_length=255, verbose_name="Họ và tên bệnh nhân")
    soCCCD = models.CharField(max_length=20, blank=True, null=True, verbose_name="Số CCCD")
    ngaySinh = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày sinh")
    gioiTinh = models.SmallIntegerField(blank=True, null=True, verbose_name="Giới tính")
    nhomMau = models.CharField(max_length=10, blank=True, null=True, verbose_name="Nhóm máu")
    maQuocTich = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã quốc tịch")
    maDanToc = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã dân tộc")
    maNgheNghiep = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã nghề nghiệp")
    diaChi = models.CharField(max_length=255, blank=True, null=True, verbose_name="Địa chỉ")
    maTinhCuTru = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã tỉnh cư trú")
    maHuyenCuTru = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã huyện cư trú")
    maXaCuTru = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã xã cư trú")
    dienThoai = models.CharField(max_length=20, blank=True, null=True, verbose_name="Số điện thoại")
    maTheBHYT = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã thẻ BHYT")
    maDKBD = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã đăng ký KCB ban đầu")
    gtTheTu = models.CharField(max_length=255, blank=True, null=True, verbose_name="Giá trị thẻ từ")
    gtTheDen = models.CharField(max_length=255, blank=True, null=True, verbose_name="Giá trị thẻ đến")
    ngayMienCCT = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày miễn cùng chi trả")
    lyDoVV = models.TextField(blank=True, null=True, verbose_name="Lý do vào viện")
    lyDoVNT = models.TextField(blank=True, null=True, verbose_name="Lý do vào nội trú")
    maLyDoVNT = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã lý do vào nội trú")
    chanDoanVao = models.TextField(blank=True, null=True, verbose_name="Chẩn đoán vào")
    chanDoanRV = models.TextField(blank=True, null=True, verbose_name="Chẩn đoán ra viện")
    maBenhChinh = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã bệnh chính")
    maBenhKT = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã bệnh kèm theo")
    maBenhYHCT = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã bệnh y học cổ truyền")
    maPTTTQT = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã phẫu thuật thủ thuật quốc tế")
    maDoiTuongKCB = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã đối tượng KCB")
    maNoiDi = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã nơi đi")
    maNoiDen = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã nơi đến")
    maTaiNan = models.IntegerField(blank=True, null=True, verbose_name="Mã tai nạn")
    ngayVao = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày vào")
    ngayVaoNoiTru = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày vào nội trú")
    ngayRa = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày ra")
    giayChuyenTuyen = models.CharField(max_length=50, blank=True, null=True, verbose_name="Giấy chuyển tuyến")
    soNgayDtri = models.IntegerField(blank=True, null=True, verbose_name="Số ngày điều trị")
    ppDieuTri = models.TextField(blank=True, null=True, verbose_name="Phương pháp điều trị")
    ketQuaDtri = models.IntegerField(blank=True, null=True, verbose_name="Kết quả điều trị")
    maLoaiRV = models.IntegerField(blank=True, null=True, verbose_name="Mã loại ra viện")
    ghiChu = models.TextField(blank=True, null=True, verbose_name="Ghi chú")
    ngayTToan = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày thanh toán")
    tThuoc = models.FloatField(blank=True, null=True, verbose_name="Tổng chi thuốc")
    tVTYT = models.FloatField(blank=True, null=True, verbose_name="Tổng chi vật tư y tế")
    tTongChiBV = models.FloatField(blank=True, null=True, verbose_name="Tổng chi bệnh viện")
    tTongChiBH = models.FloatField(blank=True, null=True, verbose_name="Tổng chi bảo hiểm")
    tBNTT = models.FloatField(blank=True, null=True, verbose_name="Bệnh nhân thanh toán")
    tBNCCT = models.FloatField(blank=True, null=True, verbose_name="Bệnh nhân cùng chi trả")
    tBHTT = models.FloatField(blank=True, null=True, verbose_name="Bảo hiểm thanh toán")
    tNguonKhac = models.FloatField(blank=True, null=True, verbose_name="Nguồn khác")
    tBHTTGDV = models.FloatField(blank=True, null=True, verbose_name="BHTT giá dịch vụ")
    namQT = models.IntegerField(blank=True, null=True, verbose_name="Năm quyết toán")
    thangQT = models.IntegerField(blank=True, null=True, verbose_name="Tháng quyết toán")
    maLoaiKCB = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã loại KCB")
    maKhoa = models.CharField(max_length=100, blank=True, null=True, verbose_name="Mã khoa")
    maCSKCB = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã cơ sở KCB")
    maKhuVuc = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã khu vực")
    canNang = models.CharField(max_length=10, blank=True, null=True, verbose_name="Cân nặng")
    canNangCon = models.CharField(max_length=100, blank=True, null=True, verbose_name="Cân nặng con")
    namNamLienTuc = models.CharField(max_length=10, blank=True, null=True, verbose_name="Năm năm liên tục")
    ngayTaiKham = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày tái khám")
    maHSBA = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã hồ sơ bệnh án")
    maTTDV = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã trung tâm dịch vụ")
    duPhong = models.TextField(blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")
    maTinh = models.IntegerField(blank=True, null=True, verbose_name="Mã tỉnh")

    class Meta:
        verbose_name = "Thông tin hành chính"
        verbose_name_plural = "Thông tin hành chính"
        db_table = "xml1_model"

    def __str__(self):
        return f"{self.maLK} - {self.hoTen}"


class XML2Model(models.Model):
    """
    XML2Model - Thông tin về thuốc
    """
    maLK = models.CharField(max_length=100, verbose_name="Mã liên kết")
    stt = models.IntegerField(blank=True, null=True, verbose_name="Số thứ tự")
    maThuoc = models.CharField(max_length=255, verbose_name="Mã thuốc")
    maPPCheBien = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã phương pháp chế biến")
    maCSKCBThuoc = models.CharField(max_length=100, blank=True, null=True, verbose_name="Mã CSKCB của thuốc")
    maNhom = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã nhóm thuốc")
    tenThuoc = models.CharField(max_length=1024, verbose_name="Tên thuốc")
    donViTinh = models.CharField(max_length=50, blank=True, null=True, verbose_name="Đơn vị tính")
    hamLuong = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Hàm lượng")
    duongDung = models.CharField(max_length=50, blank=True, null=True, verbose_name="Đường dùng")
    dangBaoChe = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Dạng bào chế")
    lieuDung = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Liều dùng")
    cachDung = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Cách dùng")
    soDangKy = models.CharField(max_length=255, blank=True, null=True, verbose_name="Số đăng ký")
    ttThau = models.CharField(max_length=50, blank=True, null=True, verbose_name="Thông tin thầu")
    phamVi = models.IntegerField(blank=True, null=True, verbose_name="Phạm vi")
    tyLeTTBH = models.IntegerField(blank=True, null=True, verbose_name="Tỷ lệ thanh toán BHYT")
    soLuong = models.FloatField(verbose_name="Số lượng")
    donGia = models.FloatField(verbose_name="Đơn giá")
    thanhTienBV = models.FloatField(verbose_name="Thành tiền bệnh viện")
    thanhTienBH = models.FloatField(verbose_name="Thành tiền bảo hiểm")
    tNguonKhacNSNN = models.FloatField(blank=True, null=True, verbose_name="Tổng nguồn từ ngân sách nhà nước")
    tNguonKhacVTNN = models.FloatField(blank=True, null=True, verbose_name="Tổng nguồn từ nước ngoài")
    tNguonKhacVTTN = models.FloatField(blank=True, null=True, verbose_name="Tổng nguồn từ trong nước")
    tNguonKhacCL = models.FloatField(blank=True, null=True, verbose_name="Các nguồn còn lại")
    tNguonKhac = models.FloatField(blank=True, null=True, verbose_name="Nguồn khác")
    mucHuong = models.FloatField(blank=True, null=True, verbose_name="Mức hưởng")
    tBNTT = models.FloatField(blank=True, null=True, verbose_name="Bệnh nhân thanh toán")
    tBNCCT = models.FloatField(blank=True, null=True, verbose_name="Bệnh nhân cùng chi trả")
    tBHTT = models.FloatField(blank=True, null=True, verbose_name="Bảo hiểm thanh toán")
    maKhoa = models.CharField(max_length=50, blank=True, null=True, verbose_name="Mã khoa")
    maBacSi = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã bác sĩ")
    maDichVu = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã dịch vụ")
    ngayYL = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày y lệnh")
    ngayTHYL = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày thực hành y lệnh")
    maPTTT = models.IntegerField(blank=True, null=True, verbose_name="Mã phương thức thanh toán")
    nguonCTra = models.IntegerField(blank=True, null=True, verbose_name="Nguồn chi trả")
    vetThuongTP = models.CharField(max_length=100, blank=True, null=True, verbose_name="Vết thương tái phát")
    duPhong = models.CharField(max_length=255, blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Thông tin thuốc"
        verbose_name_plural = "Thông tin thuốc"
        db_table = "xml2_model"

    def __str__(self):
        return f"{self.maLK} - {self.tenThuoc}"


class XML3Model(models.Model):
    """
    XML3Model - Chỉ tiêu chi tiết dịch vụ kỹ thuật và vật tư y tế
    """
    maLK = models.CharField(max_length=100, verbose_name="Mã liên kết")
    stt = models.IntegerField(verbose_name="Số thứ tự")
    maDichVu = models.CharField(max_length=100, verbose_name="Mã DVKT/Khám/Giường")
    maPTTTQT = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã phẫu thuật thủ thuật quốc tế")
    maVatTu = models.CharField(max_length=255, verbose_name="Mã vật tư y tế")
    maNhom = models.IntegerField(blank=True, null=True, verbose_name="Mã nhóm theo chi phí")
    goiVTYT = models.CharField(max_length=10, blank=True, null=True, verbose_name="Gói VTYT trong một lần sử dụng DVKT")
    tenVatTu = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Tên vật tư y tế")
    tenDichVu = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Tên DVKT/Khám/Giường")
    maXangDau = models.CharField(max_length=20, blank=True, null=True, verbose_name="Mã loại xăng dầu")
    donViTinh = models.CharField(max_length=50, blank=True, null=True, verbose_name="Đơn vị tính")
    phamVi = models.IntegerField(blank=True, null=True, verbose_name="Phạm vi")
    soLuong = models.FloatField(blank=True, null=True, verbose_name="Số lượng")
    donGiaBV = models.FloatField(blank=True, null=True, verbose_name="Đơn giá bệnh viện")
    donGiaBH = models.FloatField(blank=True, null=True, verbose_name="Đơn giá bảo hiểm")
    tyLeTT = models.FloatField(blank=True, null=True, verbose_name="Tỷ lệ thanh toán")
    thanhTienBV = models.FloatField(blank=True, null=True, verbose_name="Thành tiền bệnh viện")
    thanhTienBH = models.FloatField(blank=True, null=True, verbose_name="Thành tiền bảo hiểm")
    ttThau = models.CharField(max_length=50, blank=True, null=True, verbose_name="Thông tin thầu")
    tyLeTTDV = models.IntegerField(blank=True, null=True, verbose_name="Tỷ lệ thanh toán DVKT")
    tyLeTTBH = models.IntegerField(blank=True, null=True, verbose_name="Tỷ lệ thanh toán BHYT")
    tTranTT = models.FloatField(blank=True, null=True, verbose_name="Trần thanh toán")
    mucHuong = models.IntegerField(blank=True, null=True, verbose_name="Mức hưởng")
    tNguonKhacNSNN = models.FloatField(blank=True, null=True, verbose_name="Tổng nguồn từ ngân sách nhà nước")
    tNguonKhacVTNN = models.FloatField(blank=True, null=True, verbose_name="Tồng nguồn từ nước ngoài")
    tNguonKhacVTTN = models.FloatField(blank=True, null=True, verbose_name="Tồng nguồn từ trong nước")
    tNguonKhacCL = models.FloatField(blank=True, null=True, verbose_name="Các nguồn còn lại")
    tNguonKhac = models.FloatField(blank=True, null=True, verbose_name="Thanh toán nguồn khác")
    tBNTT = models.FloatField(blank=True, null=True, verbose_name="Bệnh nhân thanh toán")
    tBNCCT = models.FloatField(blank=True, null=True, verbose_name="Bệnh nhân cùng chi trả")
    tBHTT = models.FloatField(blank=True, null=True, verbose_name="Bảo hiểm thanh toán")
    maKhoa = models.CharField(max_length=50, blank=True, null=True, verbose_name="Mã khoa")
    maGiuong = models.CharField(max_length=50, blank=True, null=True, verbose_name="Mã giường")
    maBacSi = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã bác sĩ")
    nguoiThucHien = models.CharField(max_length=255, blank=True, null=True, verbose_name="Người thực hiện")
    maBenh = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã bệnh")
    maBenhYHCT = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã bệnh YHCT")
    ngayYL = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày y lệnh (yyyyMMddHHmm)")
    ngayTHYL = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày thực hành y lệnh (yyyyMMddHHmm)")
    ngayKQ = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày kết quả (yyyyMMddHHmm)")
    maPTTT = models.IntegerField(blank=True, null=True, verbose_name="Mã Phương thức thanh toán")
    vetThuongTP = models.CharField(max_length=100, blank=True, null=True, verbose_name="Vết thương tái phát")
    ppVoCam = models.CharField(max_length=3, blank=True, null=True, verbose_name="Phương pháp vô cảm")
    viTriThDVKT = models.CharField(max_length=255, blank=True, null=True, verbose_name="Vị trí thực hiện DVKT")
    maMay = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Mã máy")
    maHieuSP = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã hiệu sản phẩm")
    taiSuDung = models.CharField(max_length=1, blank=True, null=True, verbose_name="Tái sử dụng")
    duPhong = models.TextField(blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Chỉ tiêu chi tiết dịch vụ kỹ thuật và vật tư y tế"
        verbose_name_plural = "Chỉ tiêu chi tiết dịch vụ kỹ thuật và vật tư y tế"
        db_table = "xml3_model"

    def __str__(self):
        return f"{self.maLK} - {self.tenDichVu or self.tenVatTu}"


class XML4Model(models.Model):
    """
    XML4Model - Chỉ tiêu chi tiết dịch vụ cận lâm sàng
    """
    maLK = models.CharField(max_length=100, verbose_name="Mã liên kết")
    stt = models.IntegerField(verbose_name="Số thứ tự")
    maDichVu = models.CharField(max_length=100, verbose_name="Mã dịch vụ")
    maChiSo = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã chỉ số")
    tenChiSo = models.CharField(max_length=255, blank=True, null=True, verbose_name="Tên chỉ số")
    giaTri = models.CharField(max_length=255, blank=True, null=True, verbose_name="Giá trị")
    donViDo = models.CharField(max_length=50, blank=True, null=True, verbose_name="Đơn vị đo")
    moTa = models.TextField(blank=True, null=True, verbose_name="Mô tả")
    ketLuan = models.TextField(blank=True, null=True, verbose_name="Kết luận")
    ngayKQ = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày kết quả (yyyyMMddHHmm)")
    maBSDocKQ = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã bác sĩ đọc kết quả")
    duPhong = models.TextField(blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Chỉ tiêu chi tiết dịch vụ cận lâm sàng"
        verbose_name_plural = "Chỉ tiêu chi tiết dịch vụ cận lâm sàng"
        db_table = "xml4_model"

    def __str__(self):
        return f"{self.maLK} - {self.tenChiSo or self.maDichVu}"


class XML5Model(models.Model):
    """
    XML5Model - Chỉ tiêu chi tiết diễn biến lâm sàng
    """
    maLK = models.CharField(max_length=100, verbose_name="Mã liên kết")
    stt = models.IntegerField(verbose_name="Số thứ tự")
    dienBienLS = models.TextField(blank=True, null=True, verbose_name="Diễn biến lâm sàng")
    giaiDoanBenh = models.TextField(blank=True, null=True, verbose_name="Giai đoạn bệnh")
    hoiChan = models.TextField(blank=True, null=True, verbose_name="Hội chẩn")
    phauThuat = models.TextField(blank=True, null=True, verbose_name="Phẫu thuật")
    thoiDiemDBLS = models.CharField(max_length=12, blank=True, null=True, verbose_name="Thời điểm diễn biến (yyyyMMddHHmm)")
    nguoiThucHien = models.CharField(max_length=255, blank=True, null=True, verbose_name="Người thực hiện")
    duPhong = models.TextField(blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Chỉ tiêu chi tiết diễn biến lâm sàng"
        verbose_name_plural = "Chỉ tiêu chi tiết diễn biến lâm sàng"
        db_table = "xml5_model"

    def __str__(self):
        return f"{self.maLK} - {self.thoiDiemDBLS or 'Diễn biến'}"


class XML6Model(models.Model):
    """
    XML6Model - Chỉ tiêu hồ sơ bệnh án chăm sóc và điều trị HIV/AIDS
    """
    maLK = models.CharField(max_length=100, verbose_name="Mã liên kết")
    maTheBHYT = models.TextField(verbose_name="Mã thẻ BHYT")
    soCCCD = models.TextField(blank=True, null=True, verbose_name="Số căn cước công dân")
    ngaySinh = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày sinh (yyyyMMddHHmm)")
    gioiTinh = models.IntegerField(blank=True, null=True, verbose_name="Giới tính (1: Nam, 2: Nữ)")
    diaChi = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Địa chỉ")
    maTinhCuTru = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã tỉnh cư trú")
    maHuyenCuTru = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã huyện cư trú")
    maXaCuTru = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã xã cư trú")
    ngayKDHIV = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày khẳng định HIV (yyyyMMdd)")
    noiLayMauXN = models.CharField(max_length=100, blank=True, null=True, verbose_name="Nơi lấy mẫu xét nghiệm")
    noiXNKD = models.CharField(max_length=100, blank=True, null=True, verbose_name="Nơi xét nghiệm khẳng định")
    noiBDDTARV = models.CharField(max_length=100, blank=True, null=True, verbose_name="Nơi bắt đầu điều trị ARV")
    bdDTARV = models.CharField(max_length=8, blank=True, null=True, verbose_name="Bắt đầu điều trị ARV (yyyyMMdd)")
    maPhacDoDieuTriBD = models.CharField(max_length=200, blank=True, null=True, verbose_name="Mã phác đồ điều trị ban đầu")
    maBacPhacDoBD = models.IntegerField(blank=True, null=True, verbose_name="Mã bậc phác đồ ban đầu")
    maLydoDtri = models.IntegerField(blank=True, null=True, verbose_name="Mã lý do điều trị")
    loaiDtriLao = models.IntegerField(blank=True, null=True, verbose_name="Loại điều trị lao")
    sangLocLao = models.IntegerField(blank=True, null=True, verbose_name="Sàng lọc lao")
    phacDoDtriLao = models.IntegerField(blank=True, null=True, verbose_name="Phác đồ điều trị lao")
    ngayBDDTriLao = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày bắt đầu điều trị lao (yyyyMMdd)")
    ngayKTDTriLao = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày kết thúc điều trị lao (yyyyMMdd)")
    ketQuaDTriLao = models.IntegerField(blank=True, null=True, verbose_name="Kết quả điều trị lao")
    maLydoXNTLVR = models.IntegerField(blank=True, null=True, verbose_name="Mã lý do xét nghiệm TLVR")
    ngayXNTLVR = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày xét nghiệm TLVR (yyyyMMdd)")
    kqXNTLVR = models.IntegerField(blank=True, null=True, verbose_name="Kết quả xét nghiệm TLVR")
    ngayKQXNTLVR = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày kết quả xét nghiệm TLVR (yyyyMMdd)")
    maLoaiBN = models.IntegerField(blank=True, null=True, verbose_name="Mã loại bệnh nhân")
    giaiDoanLamSang = models.IntegerField(blank=True, null=True, verbose_name="Giải đoạn làm sáng")
    nhomDoiTuong = models.IntegerField(blank=True, null=True, verbose_name="Nhóm đối tượng")
    maTinhTrangDK = models.CharField(max_length=20, blank=True, null=True, verbose_name="Mã tình trạng đăng ký")
    lanXNPCR = models.IntegerField(blank=True, null=True, verbose_name="Lần xét nghiệm PCR")
    ngayXNPCR = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày xét nghiệm PCR (yyyyMMdd)")
    ngayKQXNPCR = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày kết quả xét nghiệm PCR (yyyyMMdd)")
    maKQXNPCR = models.IntegerField(blank=True, null=True, verbose_name="Mã kết quả xét nghiệm PCR")
    ngayNhanTTMangThai = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày nhận thông tin mang thai (yyyyMMdd)")
    ngayBatDauDTCTX = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày bắt đầu điều trị ctx (yyyyMMdd)")
    maXuTri = models.IntegerField(blank=True, null=True, verbose_name="Mã xử trí")
    ngayBatDauXuTri = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày bắt đầu xử trí (yyyyMMdd)")
    ngayKetThucXuTri = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày kết thúc xử trí (yyyyMMdd)")
    maPhacDoDieuTri = models.CharField(max_length=200, blank=True, null=True, verbose_name="Mã phác đồ điều trị")
    maBacPhacDo = models.IntegerField(blank=True, null=True, verbose_name="Mã bậc phác đồ")
    soNgayCapThuocARV = models.IntegerField(blank=True, null=True, verbose_name="Số ngày cấp thuốc ARV")
    ngayChuyenPhacDo = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày chuyển phác đồ (yyyyMMdd)")
    lyDoChuyenPhacDo = models.IntegerField(blank=True, null=True, verbose_name="Lý do chuyển phác đồ")
    maCSKCB = models.CharField(max_length=5, blank=True, null=True, verbose_name="Mã cơ sở khám chữa bệnh")
    duPhong = models.TextField(blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Chỉ tiêu hồ sơ bệnh án chăm sóc và điều trị HIV/AIDS"
        verbose_name_plural = "Chỉ tiêu hồ sơ bệnh án chăm sóc và điều trị HIV/AIDS"
        db_table = "xml6_model"

    def __str__(self):
        return f"{self.maLK} - {self.maTheBHYT}"


class XML7Model(models.Model):
    """
    XML7Model - Chỉ tiêu dữ liệu giấy ra viện
    """
    maLK = models.CharField(max_length=100, verbose_name="Mã liên kết")
    soLuuTru = models.CharField(max_length=200, blank=True, null=True, verbose_name="Số lưu trữ")
    maYTe = models.CharField(max_length=200, blank=True, null=True, verbose_name="Mã y tế")
    maKhoaRV = models.CharField(max_length=200, blank=True, null=True, verbose_name="Mã khoa ra viện")
    ngayVao = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày vào (yyyyMMddHHmm)")
    ngayRa = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày ra (yyyyMMddHHmm)")
    maDinhChiThai = models.IntegerField(blank=True, null=True, verbose_name="Mã định chỉ thai")
    nguyenNhanDinhChi = models.TextField(blank=True, null=True, verbose_name="Nguyên nhân định chỉ")
    thoiGianDinhChi = models.CharField(max_length=12, blank=True, null=True, verbose_name="Thời gian định chỉ (yyyyMMddHHmm)")
    tuoiThai = models.IntegerField(blank=True, null=True, verbose_name="Tuổi thai (tuần)")
    chanDoanRV = models.CharField(max_length=1500, blank=True, null=True, verbose_name="Chẩn đoán ra viện")
    ppDieuTri = models.TextField(blank=True, null=True, verbose_name="Phương pháp điều trị")
    ghiChu = models.CharField(max_length=1500, blank=True, null=True, verbose_name="Ghi chú")
    maTTDV = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã trung tâm dịch vụ")
    maBS = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã bác sĩ")
    tenBS = models.CharField(max_length=255, blank=True, null=True, verbose_name="Tên bác sĩ")
    ngayCT = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày chứng từ (yyyyMMdd)")
    maCha = models.CharField(max_length=50, blank=True, null=True, verbose_name="Mã cha")
    maMe = models.CharField(max_length=50, blank=True, null=True, verbose_name="Mã mẹ")
    maTheTam = models.CharField(max_length=15, blank=True, null=True, verbose_name="Mã thẻ tạm")
    hoTenCha = models.CharField(max_length=255, blank=True, null=True, verbose_name="Họ tên cha")
    hoTenMe = models.CharField(max_length=255, blank=True, null=True, verbose_name="Họ tên mẹ")
    soNgayNghi = models.IntegerField(blank=True, null=True, verbose_name="Số ngày nghỉ")
    ngoaiTruTuNgay = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngoại trú từ ngày (yyyyMMdd)")
    ngoaiTruDenNgay = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngoại trú đến ngày (yyyyMMdd)")
    duPhong = models.TextField(blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Chỉ tiêu dữ liệu giấy ra viện"
        verbose_name_plural = "Chỉ tiêu dữ liệu giấy ra viện"
        db_table = "xml7_model"

    def __str__(self):
        return f"{self.maLK} - {self.chanDoanRV or 'Giấy ra viện'}"


class XML8Model(models.Model):
    """
    XML8Model - Chỉ tiêu dữ liệu tóm tắt hồ sơ bệnh án
    """
    maLK = models.CharField(max_length=50, verbose_name="Mã liên kết")
    maLoaiKCB = models.CharField(max_length=2, verbose_name="Mã loại KCB")
    hoTenCha = models.CharField(max_length=255, blank=True, null=True, verbose_name="Họ tên cha")
    hoTenMe = models.CharField(max_length=255, blank=True, null=True, verbose_name="Họ tên mẹ")
    nguoiGiamHo = models.CharField(max_length=255, blank=True, null=True, verbose_name="Người giám hộ")
    donVi = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Đơn vị")
    ngayVao = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày vào (yyyyMMddHHmm)")
    ngayRa = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày ra (yyyyMMddHHmm)")
    chanDoanVao = models.TextField(blank=True, null=True, verbose_name="Chẩn đoán vào")
    chanDoanRV = models.TextField(blank=True, null=True, verbose_name="Chẩn đoán ra viện")
    qtBenhLy = models.TextField(blank=True, null=True, verbose_name="Quá trình bệnh lý")
    tomtatKQ = models.TextField(blank=True, null=True, verbose_name="Tóm tắt kết quả")
    ppDieuTri = models.TextField(blank=True, null=True, verbose_name="Phương pháp điều trị")
    ngaySinhCon = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày sinh con (yyyyMMdd)")
    ngayConChet = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày con chết (yyyyMMdd)")
    soConChet = models.IntegerField(blank=True, null=True, verbose_name="Số con chết")
    ketQuaDtri = models.IntegerField(blank=True, null=True, verbose_name="Kết quả điều trị")
    ghiChu = models.TextField(blank=True, null=True, verbose_name="Ghi chú")
    maTTDV = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã trung tâm dịch vụ")
    ngayCT = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày chứng từ (yyyyMMdd)")
    maTheTam = models.CharField(max_length=15, blank=True, null=True, verbose_name="Mã thẻ tạm")
    duPhong = models.TextField(blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Chỉ tiêu dữ liệu tóm tắt hồ sơ bệnh án"
        verbose_name_plural = "Chỉ tiêu dữ liệu tóm tắt hồ sơ bệnh án"
        db_table = "xml8_model"

    def __str__(self):
        return f"{self.maLK} - {self.maLoaiKCB}"


class XML9Model(models.Model):
    """
    XML9Model - Chỉ tiêu dữ liệu giấy chứng sinh
    """
    maLK = models.CharField(max_length=100, verbose_name="Mã liên kết")
    maBHXHNND = models.CharField(max_length=20, blank=True, null=True, verbose_name="Mã BHXH người nhà")
    maTheNND = models.CharField(max_length=20, blank=True, null=True, verbose_name="Mã thẻ người nhà")
    hoTenNND = models.CharField(max_length=255, blank=True, null=True, verbose_name="Họ tên người nhà")
    ngaySinhNND = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày sinh người nhà (yyyyMMdd)")
    maDanTocNND = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã dân tộc người nhà")
    soCCCDNND = models.TextField(blank=True, null=True, verbose_name="Số CCCD người nhà")
    ngayCapCCCDNND = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày cấp CCCD người nhà (yyyyMMdd)")
    noiCapCCCDNND = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Nơi cấp CCCD người nhà")
    noiCuTruNND = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Nơi cư trú người nhà")
    maQuocTich = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã quốc tịch")
    maTinhCuTru = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã tỉnh cư trú")
    maHuyenCuTru = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã huyện cư trú")
    maXaCuTru = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã xã cư trú")
    hoTenCha = models.CharField(max_length=255, blank=True, null=True, verbose_name="Họ tên cha")
    maTheTam = models.CharField(max_length=15, blank=True, null=True, verbose_name="Mã thẻ tạm")
    hoTenCon = models.CharField(max_length=255, blank=True, null=True, verbose_name="Họ tên con")
    gioiTinhCon = models.IntegerField(blank=True, null=True, verbose_name="Giới tính con")
    soCon = models.IntegerField(blank=True, null=True, verbose_name="Số con")
    lanSinh = models.IntegerField(blank=True, null=True, verbose_name="Lần sinh")
    soConSong = models.IntegerField(blank=True, null=True, verbose_name="Số con sống")
    canNangCon = models.FloatField(blank=True, null=True, verbose_name="Cân nặng con")
    ngaySinhCon = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày sinh con (yyyyMMddHHmm)")
    noiSinhCon = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Nơi sinh con")
    tinhTrangCon = models.TextField(blank=True, null=True, verbose_name="Tình trạng con")
    sinhConPhauThuat = models.IntegerField(blank=True, null=True, verbose_name="Sinh con phẫu thuật")
    sinhConDuoi32Tuan = models.IntegerField(blank=True, null=True, verbose_name="Sinh con dưới 32 tuần")
    ghiChu = models.TextField(blank=True, null=True, verbose_name="Ghi chú")
    nguoiDoDe = models.CharField(max_length=255, blank=True, null=True, verbose_name="Người đỡ đẻ")
    nguoiGhiPhieu = models.CharField(max_length=255, blank=True, null=True, verbose_name="Người ghi phiếu")
    ngayCT = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày chứng từ (yyyyMMdd)")
    so = models.CharField(max_length=200, blank=True, null=True, verbose_name="Số")
    quyenSo = models.CharField(max_length=200, blank=True, null=True, verbose_name="Quyển số")
    maTTDV = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã trung tâm dịch vụ")
    duPhong = models.TextField(blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Chỉ tiêu dữ liệu giấy chứng sinh"
        verbose_name_plural = "Chỉ tiêu dữ liệu giấy chứng sinh"
        db_table = "xml9_model"

    def __str__(self):
        return f"{self.maLK} - {self.hoTenNND or self.hoTenCon}"


class XML10Model(models.Model):
    """
    XML10Model - Chỉ tiêu dữ liệu giấy chứng nhận nghỉ dưỡng thai
    """
    maLK = models.CharField(max_length=100, verbose_name="Mã liên kết")
    soSeri = models.CharField(max_length=200, verbose_name="Số seri")
    soCT = models.CharField(max_length=200, verbose_name="Số chứng từ")
    soNgay = models.IntegerField(verbose_name="Số ngày")
    donVi = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Đơn vị")
    chanDoanRV = models.TextField(verbose_name="Chẩn đoán ra viện")
    tuNgay = models.CharField(max_length=8, verbose_name="Từ ngày (yyyyMMdd)")
    denNgay = models.CharField(max_length=8, verbose_name="Đến ngày (yyyyMMdd)")
    maTTDV = models.CharField(max_length=255, verbose_name="Mã Thủ trưởng đơn vị")
    maBS = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã bác sĩ")
    tenBS = models.CharField(max_length=255, blank=True, null=True, verbose_name="Tên bác sĩ")
    ngayCT = models.CharField(max_length=8, verbose_name="Ngày chứng từ (yyyyMMd)")
    duPhong = models.TextField(blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Chỉ tiêu dữ liệu giấy chứng nhận nghỉ dưỡng thai"
        verbose_name_plural = "Chỉ tiêu dữ liệu giấy chứng nhận nghỉ dưỡng thai"
        db_table = "xml10_model"

    def __str__(self):
        return f"{self.maLK} - {self.soCT}"


class XML11Model(models.Model):
    """
    XML11Model - Chỉ tiêu dữ liệu giấy chứng nhận nghỉ việc hưởng bảo hiểm xã hội
    """
    maLK = models.CharField(max_length=100, verbose_name="Mã liên kết")
    soCT = models.CharField(max_length=200, verbose_name="Số chứng từ")
    soSeri = models.CharField(max_length=200, verbose_name="Số seri")
    soKCB = models.CharField(max_length=200, blank=True, null=True, verbose_name="Số khám chữa bệnh")
    donVi = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Đơn vị")
    maBHXH = models.CharField(max_length=20, blank=True, null=True, verbose_name="Mã BHXH")
    maTheBHYT = models.TextField(verbose_name="Mã thẻ BHYT")
    chanDoanRV = models.TextField(verbose_name="Chẩn đoán ra viện")
    ppDieuTri = models.TextField(blank=True, null=True, verbose_name="Phương pháp điều trị")
    maDinhChiThai = models.IntegerField(blank=True, null=True, verbose_name="Mã định chỉ thai")
    nguyenNhanDinhChi = models.TextField(blank=True, null=True, verbose_name="Nguyên nhân định chỉ")
    tuoiThai = models.IntegerField(blank=True, null=True, verbose_name="Tuổi thai (tuần)")
    soNgayNghi = models.IntegerField(blank=True, null=True, verbose_name="Số ngày nghỉ")
    tuNgay = models.CharField(max_length=8, blank=True, null=True, verbose_name="Từ ngày (yyyyMMdd)")
    denNgay = models.CharField(max_length=8, blank=True, null=True, verbose_name="Đến ngày (yyyyMMdd)")
    hoTenCha = models.CharField(max_length=255, blank=True, null=True, verbose_name="Họ tên cha")
    hoTenMe = models.CharField(max_length=255, blank=True, null=True, verbose_name="Họ tên mẹ")
    maTTDV = models.CharField(max_length=255, verbose_name="Mã Thủ trưởng đơn vị")
    maBS = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã bác sĩ")
    ngayCT = models.CharField(max_length=8, verbose_name="Ngày chứng từ (yyyyMMdd)")
    maTheTam = models.CharField(max_length=15, blank=True, null=True, verbose_name="Mã thẻ tạm")
    mauSo = models.CharField(max_length=10, verbose_name="Mã mẫu số")
    duPhong = models.TextField(blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Chỉ tiêu dữ liệu giấy chứng nhận nghỉ việc hưởng bảo hiểm xã hội"
        verbose_name_plural = "Chỉ tiêu dữ liệu giấy chứng nhận nghỉ việc hưởng bảo hiểm xã hội"
        db_table = "xml11_model"

    def __str__(self):
        return f"{self.maLK} - {self.soCT}"


class XML12Model(models.Model):
    """
    XML12Model - Chỉ tiêu dữ liệu giám định y khoa
    """
    nguoiChuTri = models.CharField(max_length=255, verbose_name="Người chủ trì")
    chucVu = models.IntegerField(blank=True, null=True, verbose_name="Chức vụ")
    ngayHop = models.CharField(max_length=8, verbose_name="Ngày họp (yyyyMM)")
    hoTen = models.CharField(max_length=255, verbose_name="Họ tên")
    ngaySinh = models.CharField(max_length=8, verbose_name="Ngày sinh (yyyyMMdd)")
    soCCCD = models.TextField(blank=True, null=True, verbose_name="Số căn cước công dân")
    ngayCapCCCD = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày cấp CCCD (yyyyMMdd)")
    noiCapCCCD = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Nơi cấp CCCD")
    diaChi = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Địa chỉ")
    maTinhCuTru = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã tỉnh cư trú")
    maHuyenCuTru = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã huyện cư trú")
    maXaCuTru = models.CharField(max_length=10, blank=True, null=True, verbose_name="Mã xã cư trú")
    maBHXH = models.CharField(max_length=20, blank=True, null=True, verbose_name="Mã BHXH")
    maTheBHYT = models.CharField(max_length=20, verbose_name="Mã thẻ BHYT")
    ngheNghiep = models.CharField(max_length=255, blank=True, null=True, verbose_name="Nghề nghiệp")
    dienThoai = models.CharField(max_length=15, blank=True, null=True, verbose_name="Điện thoại")
    maDoiTuong = models.CharField(max_length=20, blank=True, null=True, verbose_name="Mã đối tượng")
    khamGiamDinh = models.IntegerField(blank=True, null=True, verbose_name="Khám giám định")
    soBienBan = models.CharField(max_length=200, blank=True, null=True, verbose_name="Số biên bản")
    tyLeTTCTCu = models.IntegerField(blank=True, null=True, verbose_name="Tỷ lệ thương tật cũ (%)")
    dangHuongCheDo = models.CharField(max_length=10, blank=True, null=True, verbose_name="Dạng hướng chế độ")
    ngayChungTu = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày chứng từ (yyyyMMdd)")
    soGiayGioiThieu = models.CharField(max_length=200, blank=True, null=True, verbose_name="Số giấy giới thiệu")
    ngayDeNghi = models.CharField(max_length=8, blank=True, null=True, verbose_name="Ngày đề nghị")
    maDonVi = models.CharField(max_length=200, blank=True, null=True, verbose_name="Mã đơn vị")
    gioiThieuCua = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Giới thiệu của")
    ketQuaKham = models.TextField(blank=True, null=True, verbose_name="Kết quả khám")
    soVanBanCanCu = models.CharField(max_length=200, blank=True, null=True, verbose_name="Số văn bản căn cứ")
    tyLeTTCTMoi = models.IntegerField(blank=True, null=True, verbose_name="Tỷ lệ thương tật mới (%)")
    tongTyLeTTCT = models.IntegerField(verbose_name="Tổng tỷ lệ thương tật (%)")
    dangKhuyetTat = models.IntegerField(blank=True, null=True, verbose_name="Dạng khuyết tật")
    mucDoKhuyetTat = models.IntegerField(blank=True, null=True, verbose_name="Mức độ khuyết tật")
    deNghi = models.TextField(blank=True, null=True, verbose_name="Đề nghị")
    duocXacDinh = models.TextField(blank=True, null=True, verbose_name="Được xác định")
    duPhong = models.TextField(blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Chỉ tiêu dữ liệu giám định y khoa"
        verbose_name_plural = "Chỉ tiêu dữ liệu giám định y khoa"
        db_table = "xml12_model"

    def __str__(self):
        return f"{self.hoTen} - {self.ngayHop}"


class XML13Model(models.Model):
    """
    XML13Model - Chỉ tiêu chi tiết thuốc
    """
    maLK = models.CharField(max_length=100, verbose_name="Mã liên kết")
    stt = models.IntegerField(verbose_name="Số thứ tự")
    maThuoc = models.CharField(max_length=255, verbose_name="Mã thuốc")
    maNhom = models.IntegerField(blank=True, null=True, verbose_name="Mã nhóm theo chi phí")
    tenThuoc = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Tên thuốc")
    donViTinh = models.CharField(max_length=50, blank=True, null=True, verbose_name="Đơn vị tính")
    hamLuong = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Hàm lượng")
    duongDung = models.CharField(max_length=255, blank=True, null=True, verbose_name="Đường dùng")
    lieuDung = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Liều dùng")
    soLuong = models.FloatField(blank=True, null=True, verbose_name="Số lượng")
    donGiaBV = models.FloatField(blank=True, null=True, verbose_name="Đơn giá bệnh viện")
    donGiaBH = models.FloatField(blank=True, null=True, verbose_name="Đơn giá bảo hiểm")
    tyLeTT = models.FloatField(blank=True, null=True, verbose_name="Tỷ lệ thanh toán")
    thanhTienBV = models.FloatField(blank=True, null=True, verbose_name="Thành tiền bệnh viện")
    thanhTienBH = models.FloatField(blank=True, null=True, verbose_name="Thành tiền bảo hiểm")
    ttThau = models.CharField(max_length=50, blank=True, null=True, verbose_name="Thông tin thầu")
    tyLeTTBH = models.IntegerField(blank=True, null=True, verbose_name="Tỷ lệ thanh toán BHYT")
    tTranTT = models.FloatField(blank=True, null=True, verbose_name="Trần thanh toán")
    mucHuong = models.IntegerField(blank=True, null=True, verbose_name="Mức hưởng")
    tNguonKhacNSNN = models.FloatField(blank=True, null=True, verbose_name="Tổng nguồn từ ngân sách nhà nước")
    tNguonKhacVTNN = models.FloatField(blank=True, null=True, verbose_name="Tồng nguồn từ nước ngoài")
    tNguonKhacVTTN = models.FloatField(blank=True, null=True, verbose_name="Tồng nguồn từ trong nước")
    tNguonKhacCL = models.FloatField(blank=True, null=True, verbose_name="Các nguồn còn lại")
    tNguonKhac = models.FloatField(blank=True, null=True, verbose_name="Thanh toán nguồn khác")
    tBNTT = models.FloatField(blank=True, null=True, verbose_name="Bệnh nhân thanh toán")
    tBNCCT = models.FloatField(blank=True, null=True, verbose_name="Bệnh nhân cùng chi trả")
    tBHTT = models.FloatField(blank=True, null=True, verbose_name="Bảo hiểm thanh toán")
    maKhoa = models.CharField(max_length=50, blank=True, null=True, verbose_name="Mã khoa")
    maBacSi = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã bác sĩ")
    nguoiThucHien = models.CharField(max_length=255, blank=True, null=True, verbose_name="Người thực hiện")
    maBenh = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã bệnh")
    maBenhYHCT = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã bệnh YHCT")
    ngayYL = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày y lệnh (yyyyMMddHHmm)")
    ngayDung = models.CharField(max_length=12, blank=True, null=True, verbose_name="Ngày dùng (yyyyMMddHHmm)")
    maPTTT = models.IntegerField(blank=True, null=True, verbose_name="Mã Phương thức thanh toán")
    duPhong = models.TextField(blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Chỉ tiêu chi tiết thuốc"
        verbose_name_plural = "Chỉ tiêu chi tiết thuốc"
        db_table = "xml13_model"

    def __str__(self):
        return f"{self.maLK} - {self.tenThuoc}"


class XML14Model(models.Model):
    """
    XML14Model - Chỉ tiêu dữ liệu giấy chuyển tuyến
    """
    maLK = models.CharField(max_length=100, verbose_name="Mã liên kết")
    soHoSo = models.CharField(max_length=200, verbose_name="Số hồ sơ")
    soChuyenTuyen = models.CharField(max_length=200, verbose_name="Số chuyển tuyến")
    giayChuyenTuyen = models.CharField(max_length=200, blank=True, null=True, verbose_name="Giấy chuyển tuyến")
    maCSKCB = models.CharField(max_length=5, verbose_name="Mã cơ sở KCB")
    maNoiDi = models.CharField(max_length=5, verbose_name="Mã nơi đi")
    maNoiDen = models.CharField(max_length=5, verbose_name="Mã nơi đến")
    hoTen = models.CharField(max_length=255, verbose_name="Họ tên")
    ngaySinh = models.CharField(max_length=8, verbose_name="Ngày sinh (yyyyMMdd)")
    gioiTinh = models.IntegerField(verbose_name="Giới tính (1: Nam, 2: Nữ)")
    maTheBHYT = models.TextField(verbose_name="Mã thẻ BHYT")
    gtTheDen = models.CharField(max_length=8, blank=True, null=True, verbose_name="Giá trị thẻ đến (yyyyMMdd)")
    chanDoanRV = models.TextField(verbose_name="Chẩn đoán ra viện")
    qtBenhLy = models.TextField(blank=True, null=True, verbose_name="Quá trình bệnh lý")
    huongDieuTri = models.TextField(blank=True, null=True, verbose_name="Hướng điều trị")
    maTTDV = models.CharField(max_length=255, verbose_name="Mã Thủ trưởng đơn vị")
    ngayCT = models.CharField(max_length=8, verbose_name="Ngày chứng từ (yyyyMMdd)")
    maTheTam = models.CharField(max_length=15, blank=True, null=True, verbose_name="Mã thẻ tạm")
    duPhong = models.TextField(blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Chỉ tiêu dữ liệu giấy chuyển tuyến"
        verbose_name_plural = "Chỉ tiêu dữ liệu giấy chuyển tuyến"
        db_table = "xml14_model"

    def __str__(self):
        return f"{self.maLK} - {self.hoTen}"


class XML15Model(models.Model):
    """
    XML15Model - Chỉ tiêu dữ liệu giấy hẹn khám
    """
    maLK = models.CharField(max_length=100, verbose_name="Mã liên kết")
    soPhieu = models.CharField(max_length=200, verbose_name="Số phiếu")
    hoTen = models.CharField(max_length=255, verbose_name="Họ tên")
    ngaySinh = models.CharField(max_length=8, verbose_name="Ngày sinh (yyyyMMdd)")
    gioiTinh = models.IntegerField(verbose_name="Giới tính (1: Nam, 2: Nữ)")
    diaChi = models.CharField(max_length=1024, blank=True, null=True, verbose_name="Địa chỉ")
    maTheBHYT = models.TextField(verbose_name="Mã thẻ BHYT")
    chanDoan = models.TextField(verbose_name="Chẩn đoán")
    ppDieuTri = models.TextField(blank=True, null=True, verbose_name="Phương pháp điều trị")
    ghiChu = models.TextField(blank=True, null=True, verbose_name="Ghi chú")
    ngayHenKham = models.CharField(max_length=8, verbose_name="Ngày hẹn khám (yyyyMMdd)")
    maTTDV = models.CharField(max_length=255, verbose_name="Mã Thủ trưởng đơn vị")
    maBS = models.CharField(max_length=255, blank=True, null=True, verbose_name="Mã bác sĩ")
    ngayCT = models.CharField(max_length=8, verbose_name="Ngày chứng từ (yyyyMMdd)")
    maTheTam = models.CharField(max_length=15, blank=True, null=True, verbose_name="Mã thẻ tạm")
    duPhong = models.TextField(blank=True, null=True, verbose_name="Dự phòng")
    ngayTao = models.DateTimeField(auto_now_add=True, verbose_name="Ngày tạo")
    ngayChinhSua = models.DateTimeField(auto_now=True, verbose_name="Ngày chỉnh sửa")
    trangThaiGuiBHXH = models.SmallIntegerField(default=0, verbose_name="Trạng thái gửi BHXH")

    class Meta:
        verbose_name = "Chỉ tiêu dữ liệu giấy hẹn khám"
        verbose_name_plural = "Chỉ tiêu dữ liệu giấy hẹn khám"
        db_table = "xml15_model"

    def __str__(self):
        return f"{self.maLK} - {self.hoTen}"