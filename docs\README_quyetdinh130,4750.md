# Module Quản lý dữ liệu XML theo Quyết định 4750 (T<PERSON><PERSON> hợp Web)

## Giới thiệu

Module Quản lý dữ liệu XML theo Quyết định 4750 là một phần mở rộng được tích hợp vào hệ thống quản lý bệnh viện hiện có, đư<PERSON><PERSON> phát triển để hỗ trợ các cơ sở y tế trong việc quản lý, xử lý và gửi dữ liệu khám chữa bệnh BHYT theo định dạng XML theo quy định của Bảo hiểm Xã hội Việt Nam.

Quyết định 4750/QĐ-BYT của Bộ Y tế quy định về việc triển khai ứng dụng công nghệ thông tin trong quản lý khám bệnh, chữa bệnh và thanh toán bảo hiểm y tế. <PERSON> đ<PERSON>, c<PERSON><PERSON> cơ sở y tế phải gửi dữ liệu khám chữa bệnh BHYT theo định dạng XML chuẩn để phục vụ công tác giám định và thanh toán chi phí khám chữa bệnh.

Module này được tích hợp liền mạch vào hệ thống quản lý bệnh viện hiện có, tận dụng cơ sở dữ liệu và cơ sở hạ tầng sẵn có, đồng thời mở rộng chức năng để đáp ứng yêu cầu quản lý dữ liệu XML theo Quyết định 4750. Việc tích hợp này giúp tận dụng tối đa dữ liệu hiện có, tránh nhập liệu trùng lặp và đảm bảo tính nhất quán của dữ liệu.

### Đặc điểm nổi bật

- **Tích hợp liền mạch**: Hoạt động như một phần của hệ thống quản lý bệnh viện hiện có, tận dụng dữ liệu và cơ sở hạ tầng sẵn có
- **Quản lý toàn diện**: Hỗ trợ đầy đủ 15 loại mẫu XML theo quy định của Quyết định 4750
- **Giao diện web thống nhất**: Giao diện web đồng nhất với hệ thống hiện có, dễ sử dụng với các chức năng được tổ chức hợp lý
- **Tự động hóa**: Tự động tính toán các giá trị tài chính, kiểm tra tính hợp lệ của dữ liệu
- **Tích hợp API**: Kết nối trực tiếp với cổng tiếp nhận của BHXH để gửi dữ liệu và nhận phản hồi
- **Bảo mật**: Tận dụng hệ thống bảo mật hiện có với cơ chế mã hóa, HTTPS và phân quyền người dùng
- **Đa người dùng**: Tận dụng hệ thống quản lý người dùng hiện có với phân quyền chi tiết
- **Đồng bộ dữ liệu**: Tự động đồng bộ dữ liệu giữa hệ thống quản lý bệnh viện và module XML

## Mục lục

- [Giới thiệu](#giới-thiệu)
- [Cấu trúc dự án](#cấu-trúc-dự-án)
- [Mô hình dữ liệu](#mô-hình-dữ-liệu)
- [Cấu trúc file XML](#cấu-trúc-file-xml)
- [Chức năng chính](#chức-năng-chính)
- [Quy trình làm việc](#quy-trình-làm-việc)
- [Cài đặt và cấu hình](#cài-đặt-và-cấu-hình)
- [Yêu cầu hệ thống](#yêu-cầu-hệ-thống)
- [Tài liệu tham khảo](#tài-liệu-tham-khảo)

## Cấu trúc dự án

Dự án được tích hợp vào hệ thống quản lý bệnh viện hiện có, tuân theo mô hình MVT (Model-View-Template) của Django để đảm bảo tính module hóa và dễ bảo trì:

```
quanlybv/                   # Thư mục gốc dự án quản lý bệnh viện hiện có
├── hospitalmanager/        # Thư mục cấu hình Django chính
│   ├── __init__.py
│   ├── settings.py         # Cấu hình Django
│   ├── urls.py             # URL routing chính
│   ├── views.py            # Views chính
│   ├── asgi.py             # ASGI config
│   └── wsgi.py             # WSGI config
├── danhmuc130/             # Ứng dụng quản lý danh mục 130
├── danhmucbv/              # Ứng dụng quản lý danh mục bệnh viện
├── departments/            # Ứng dụng quản lý khoa phòng
├── devices/                # Ứng dụng quản lý thiết bị
├── emails/                 # Ứng dụng quản lý email
├── permissions/            # Ứng dụng quản lý phân quyền
├── reports/                # Ứng dụng báo cáo
├── schedules/              # Ứng dụng lịch trực
├── typingpractice/         # Ứng dụng luyện gõ
├── users/                  # Ứng dụng quản lý người dùng
├── xml4750/                # Ứng dụng mới cho Quyết định 4750 (sẽ tạo)
│   ├── __init__.py
│   ├── admin.py            # Cấu hình Django admin
│   ├── apps.py             # Cấu hình ứng dụng
│   ├── models.py           # Mô hình dữ liệu
│   ├── views.py            # Views xử lý logic
│   ├── forms.py            # Form xử lý dữ liệu
│   ├── urls.py             # URL routing của ứng dụng
│   └── utils.py            # Tiện ích
├── templates/              # Templates chung
│   ├── layouts/            # Layout chung
│   │   ├── base.html       # Template cơ sở
│   │   ├── sidebar.html    # Sidebar menu
│   ├── xml4750/            # Templates cho ứng dụng XML4750
│   │   ├── list_4750.html  # Danh sách tất cả XML0-XML15
│   │   ├── xml_edit_4750.html   # Template chung để chỉnh sửa XML (chứa cả form và danh sách)
│   ├── danhmucbv/          # Templates cho ứng dụng danhmucbv
│   ├── reports/            # Templates cho ứng dụng reports
│   └── ...
├── static/                 # Tài nguyên tĩnh toàn cục
│   ├── css/
│   ├── js/
│   ├── images/
│   └── xml4750/            # Tài nguyên tĩnh cho ứng dụng XML4750
│       ├── css/
│       ├── js/
│       └── images/
├── media/                  # File tải lên
├── logs/                   # File log
├── manage.py               # Script quản lý Django
└── requirements.txt        # Danh sách thư viện phụ thuộc
```

## Mô hình dữ liệu

Ứng dụng quản lý dữ liệu XML theo các quy định của Bộ Y tế, bao gồm Quyết định 4750 và Quyết định 130. Dữ liệu được tổ chức thành các bảng trong cơ sở dữ liệu tương ứng với các loại mẫu XML khác nhau.

### Các mẫu XML theo Quyết định 4750

Ứng dụng quản lý dữ liệu XML theo 15 loại mẫu XML khác nhau theo quy định của Quyết định 4750, mỗi loại tương ứng với một bảng trong cơ sở dữ liệu:

### Bảng 0 - Chỉ tiêu dữ liệu về trạng thái khám bệnh, chữa bệnh (Bảng check-in)

Bảng này lưu trữ thông tin về trạng thái khám bệnh, chữa bệnh của bệnh nhân theo Quyết định 130.

#### Định dạng XML

Khi import/export ra file XML, dữ liệu Bảng 0 được chuyển đổi theo cấu trúc sau:

```xml
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<CHI_TIEU_TRANG_THAI_KCB>
  <DSACH_TRANG_THAI_KCB>
    <TRANG_THAI_KCB>
      <MA_LK>23055499200520250754</MA_LK>
      <STT>1</STT>
      <MA_BN>23055499</MA_BN>
      <HO_TEN>PHAN VĂN THẮNG</HO_TEN>
      <SO_CCCD>034055007494</SO_CCCD>
      <NGAY_SINH>195501010000</NGAY_SINH>
      <GIOI_TINH>1</GIOI_TINH>
      <MA_THE_BHYT>CK2646423076034</MA_THE_BHYT>
      <MA_DKBD>64020</MA_DKBD>
      <GT_THE_TU>01/01/2025</GT_THE_TU>
      <GT_THE_DEN>31/12/2025</GT_THE_DEN>
      <MA_DOITUONG_KCB>1.1</MA_DOITUONG_KCB>
      <NGAY_VAO>202505200720</NGAY_VAO>
      <NGAY_VAO_NOI_TRU />
      <LY_DO_VNT />
      <MA_LY_DO_VNT />
      <MA_LOAI_KCB>01</MA_LOAI_KCB>
      <MA_CSKCB>64020</MA_CSKCB>
      <MA_DICH_VU>02.1896</MA_DICH_VU>
      <TEN_DICH_VU>Khám Nội</TEN_DICH_VU>
      <MA_THUOC />
      <TEN_THUOC />
      <MA_VAT_TU />
      <TEN_VAT_TU />
      <NGAY_YL>202505200754</NGAY_YL>
      <DU_PHONG />
    </TRANG_THAI_KCB>
  </DSACH_TRANG_THAI_KCB>
</CHI_TIEU_TRANG_THAI_KCB>
```

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc | Diễn giải |
|---------------------------|--------------------------------------|-----------------|--------------|----------|-----------|
| `maLK`                    | Mã liên kết                          | String          | 100          | ✅       | Là mã đợt điều trị duy nhất (dùng để liên kết giữa Bảng chỉ tiêu tổng hợp khám bệnh, chữa bệnh (Bảng XML 1) và các bảng còn lại ban hành kèm theo Quyết định này trong một lần khám bệnh, chữa bệnh (PRIMARY KEY)) |
| `maBN`                    | Mã bệnh nhân                         | String          | 100          | ✅       | Là mã người bệnh theo quy định của cơ sở KCB |
| `hoTen`                   | Họ và tên bệnh nhân                  | String          | 255          | ✅       | Họ và tên của người bệnh |
| `soCCCD`                  | Số căn cước công dân                 | String          | 1024           | ❌       | Số căn cước công dân hoặc số chứng minh thư nhân dân hoặc số hộ chiếu của người bệnh |
| `ngaySinh`                | Ngày sinh                            | String          | 12           | ✅       | Ghi ngày, tháng, năm sinh ghi trên thẻ BHYT của người bệnh, gồm 12 ký tự, bao gồm: 04 ký tự năm + 02 ký tự tháng + 02 ký tự ngày + 02 ký tự giờ + 02 ký tự phút |
| `gioiTinh`                | Giới tính                            | Integer         | 1            | ✅       | Là mã giới tính của người bệnh (1: Nam; 2: Nữ; 3: Chưa xác định) |
| `maTheBHYT`               | Mã thẻ BHYT                          | String          | 15           | ✅       | Ghi mã thẻ BHYT của người bệnh do cơ quan BHXH cấp |
| `maDKBD`                  | Mã đăng ký KCB ban đầu               | String          | 5            | ✅       | Ghi mã cơ sở KCB nơi người bệnh đăng ký ban đầu ghi trên thẻ BHYT, gồm có 05 ký tự |
| `gtTheTu`                 | Giá trị thẻ từ                       | String          | 8            | ✅       | Ghi thời điểm thẻ BHYT bắt đầu có giá trị sử dụng, gồm 08 ký tự, bao gồm: 04 ký tự năm + 02 ký tự tháng + 02 ký tự ngày |
| `gtTheDen`                | Giá trị thẻ đến                      | String          | 8            | ✅       | Ghi thời điểm thẻ BHYT hết giá trị sử dụng, gồm 08 ký tự, bao gồm: 04 ký tự năm + 02 ký tự tháng + 02 ký tự ngày |
| `maDoiTuongKCB`           | Mã đối tượng KCB                     | String          | 4            | ❌       | Ghi mã đối tượng đến KCB theo Bộ mã DMDC do Bộ trưởng Bộ Y tế ban hành |
| `ngayVao`                 | Ngày vào                             | String          | 12           | ✅       | Ghi thời điểm người bệnh đến KCB, gồm 12 ký tự, trong đó: 04 ký tự năm + 02 ký tự tháng + 02 ký tự ngày + 02 ký tự giờ (định dạng theo 24 giờ) + 02 ký tự phút |
| `ngayVaoNoiTru`           | Ngày vào nội trú                     | String          | 12           | ❌       | Ghi thời điểm người bệnh vào nội trú, gồm 12 ký tự, trong đó: 04 ký tự năm + 02 ký tự tháng + 02 ký tự ngày + 02 ký tự giờ (định dạng theo 24 giờ) + 02 ký tự phút |
| `ly_do_vnt`               | Lý do vào nội trú                    | String          | n           | ❌       | Ghi lý do vào nội trú, áp dụng đối với trường hợp điều trị nội trú hoặc điều trị ban ngày (bao gồm cả triệu chứng lâm sàng hoặc các lý do khác khiến cho người bệnh đến cơ sở KBCB). |
| `ma_ly_do_vnt`            | Lý do vào nội trú                    | String          | 5           | ❌       | Ghi mã lý do người bệnh vào điều trị nội trú theo quy định của Bộ Y tế.|
| `maLoaiKCB`               | Mã loại KCB                          | String          | 2            | ❌       | Ghi mã hình thức KCB theo Bộ mã DMDC do Bộ trưởng Bộ Y tế ban hành |
| `maCSKCB`                 | Mã cơ sở KCB                         | String          | 5            | ❌       | Ghi mã cơ sở KCB nơi người bệnh đến khám bệnh, điều trị do cơ quan có thẩm quyền cấp |
| `maDichVu`                | Mã dịch vụ                           | String          | 50           | ❌       | Ghi mã dịch vụ kỹ thuật hoặc mã dịch vụ khám bệnh thực hiện cho người bệnh, theo quy định tại Bộ mã danh mục dùng chung (DMDC) do Bộ trưởng Bộ Y tế ban hành |
| `tenDichVu`               | Tên dịch vụ                          | String          | 1024         | ❌       | Ghi tên dịch vụ kỹ thuật hoặc tên dịch vụ khám bệnh |
| `ma_thuoc`                | Mã thuốc                             | String          | 255         | ❌       | Ghi mã hoạt chất của thuốc theo quy định tại Bộ mã danh mục dùng chung do Bộ Y tế ban hành trong trường hợp phát sinh chi phí đầu tiên tại khoa điều trị nội trú hoặc khoa điều trị nội trú ban ngày hoặc khoa điều trị ngoại trú là thuốc.
Đối với thuốc do cơ sở KBCB tự bào chế, pha chế: ghi mã thuốc gồm mã các hoạt chất/thành phần, cách nhau bằng dấu cộng "+". Các hoạt chất/thành phần của thuốc nằm ngoài danh mục thuốc thuộc phạm vi thanh toán của quỹ BHYT thì ghi mã hoạt chất/thành phần theo cấu trúc YYYY.SĐK, trong đó YYYY là năm cấp số đăng ký lưu hành nguyên liệu làm thuốc và SĐK là số đăng ký lưu hành nguyên liệu làm thuốc |
| `tenThuoc`                | Tên Thuốc                            | String          | 1024         | ❌       | Ghi tên thuốc tương ứng trong trường hợp phát sinh chi phí đầu tiên tại khoa điều trị nội trú hoặc khoa điều trị nội trú ban ngày hoặc khoa điều trị ngoại trú là thuốc. |
| `maVatTu`                 | Mã Vật tư                            | String          | 255         | ❌       | Ghi mã vật tư y tế trong trường hợp phát sinh chi phí đầu tiên tại khoa điều trị nội trú hoặc khoa điều trị nội trú ban ngày hoặc khoa điều trị ngoại trú là VTYT |
| `tenVatTu`                | Tên vật tư y tế                      | String          | 1024         | ❌       | Ghi tên VTYT tương ứng trong trường hợp phát sinh chi phí đầu tiên tại khoa điều trị nội trú hoặc khoa điều trị nội trú ban ngày hoặc khoa điều trị ngoại trú là VTYT. |
| `ngayYL`                  | Ngày y lệnh                          | String          | 12           | ❌       | Ghi thời điểm ra y lệnh (gồm 12 ký tự, theo cấu trúc yyyymmddHHmm, bao gồm: 04 ký tự năm + 02 ký tự tháng + 02 ký tự ngày + 02 ký tự giờ (24 giờ) + 02 ký tự phút) |
| `duPhong`                 | Dự phòng                             | String          | n           | ❌       | Trường dữ liệu dự phòng khi cần thiết |

### 1. XML1Model - Thông tin hành chính của bệnh nhân

Bảng này lưu trữ thông tin hành chính của bệnh nhân, bao gồm thông tin cá nhân, thông tin BHYT, thông tin khám chữa bệnh và thông tin tài chính.

#### Định dạng XML

Khi xuất ra file XML, dữ liệu XML1Model được chuyển đổi theo cấu trúc sau:

```xml
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<TONG_HOP>
  <MA_LK>ML123456789</MA_LK>
  <STT>1</STT>
  <MA_BN>BN123456</MA_BN>
  <HO_TEN>Nguyễn Văn A</HO_TEN>
  <SO_CCCD>012345678901</SO_CCCD>
  <NGAY_SINH>198001010000</NGAY_SINH>
  <GIOI_TINH>1</GIOI_TINH>
  <NHOM_MAU>O</NHOM_MAU>
  <MA_QUOCTICH>01</MA_QUOCTICH>
  <MA_DANTOC>01</MA_DANTOC>
  <MA_NGHE_NGHIEP>13</MA_NGHE_NGHIEP>
  <DIA_CHI>123 Đường ABC, Phường XYZ</DIA_CHI>
  <MA_TINH_CU_TRU>01</MA_TINH_CU_TRU>
  <MA_HUYEN_CU_TRU>001</MA_HUYEN_CU_TRU>
  <MA_XA_CU_TRU>00001</MA_XA_CU_TRU>
  <DIEN_THOAI>0912345678</DIEN_THOAI>
  <MA_THE_BHYT>GD4012345678901</MA_THE_BHYT>
  <MA_DKBD>01001</MA_DKBD>
  <GT_THE_TU>202001010000</GT_THE_TU>
  <GT_THE_DEN>202112310000</GT_THE_DEN>
  <NGAY_MIEN_CCT>202001010000</NGAY_MIEN_CCT>
  <LY_DO_VV>Đau bụng</LY_DO_VV>
  <LY_DO_VNT>Viêm ruột thừa</LY_DO_VNT>
  <MA_LY_DO_VNT>01</MA_LY_DO_VNT>
  <CHAN_DOAN_VAO>Đau bụng cấp</CHAN_DOAN_VAO>
  <CHAN_DOAN_RV>Viêm ruột thừa cấp</CHAN_DOAN_RV>
  <MA_BENH_CHINH>K35.8</MA_BENH_CHINH>
  <MA_BENH_KT>K35.3</MA_BENH_KT>
  <MA_BENH_YHCT></MA_BENH_YHCT>
  <MA_PTTT_QT>AAAA</MA_PTTT_QT>
  <MA_DOI_TUONG_KCB>1</MA_DOI_TUONG_KCB>
  <MA_NOI_DI>01001</MA_NOI_DI>
  <MA_NOI_DEN></MA_NOI_DEN>
  <MA_TAI_NAN>0</MA_TAI_NAN>
  <NGAY_VAO>202101010800</NGAY_VAO>
  <NGAY_VAO_NOI_TRU>202101010830</NGAY_VAO_NOI_TRU>
  <NGAY_RA>202101051400</NGAY_RA>
  <GIAY_CHUYEN_TUYEN>CT123456</GIAY_CHUYEN_TUYEN>
  <SO_NGAY_DTRI>5</SO_NGAY_DTRI>
  <PP_DIEU_TRI>Phẫu thuật cắt ruột thừa</PP_DIEU_TRI>
  <KET_QUA_DTRI>1</KET_QUA_DTRI>
  <MA_LOAI_RV>1</MA_LOAI_RV>
  <GHI_CHU>Bệnh nhân xuất viện trong tình trạng ổn định</GHI_CHU>
  <NGAY_TTOAN>202101051430</NGAY_TTOAN>
  <T_THUOC>1500000</T_THUOC>
  <T_VTYT>2000000</T_VTYT>
  <T_TONG_CHI_BV>5000000</T_TONG_CHI_BV>
  <T_TONG_CHI_BH>4500000</T_TONG_CHI_BH>
  <T_BNTT>500000</T_BNTT>
  <T_BNCCT>0</T_BNCCT>
  <T_BHTT>4500000</T_BHTT>
  <T_NGUON_KHAC>0</T_NGUON_KHAC>
  <T_BHTT_GDV>4500000</T_BHTT_GDV>
  <NAM_QT>2021</NAM_QT>
  <THANG_QT>1</THANG_QT>
  <MA_LOAI_KCB>2</MA_LOAI_KCB>
  <MA_KHOA>01</MA_KHOA>
  <MA_CSKCB>01001</MA_CSKCB>
  <MA_KHU_VUC>1</MA_KHU_VUC>
  <CAN_NANG>65</CAN_NANG>
  <CAN_NANG_CON></CAN_NANG_CON>
  <NAM_NAM_LIEN_TUC>1</NAM_NAM_LIEN_TUC>
  <NGAY_TAI_KHAM>202101150900</NGAY_TAI_KHAM>
  <MA_HSBA>HS123456</MA_HSBA>
  <MA_TTDV>01</MA_TTDV>
  <DU_PHONG></DU_PHONG>
</TONG_HOP>
```

Dữ liệu XML được mã hóa Base64 và đóng gói trong cấu trúc:

```xml
<FILEHOSO>
  <LOAIHOSO>XML1</LOAIHOSO>
  <NOIDUNGFILE>PFRPTkdfSE9QPjxNQV9MSz5NTDEyMzQ1Njc4OTwvTUFfTEs+PFNUVD4xPC9TVFQ+PE1BX0JOPkJOMTIzNDU2PC9NQV9CTj48SE9fVEVOPk5ndXnhu4VuIFbEg24gQTwvSE9fVEVOPjxTT19DQ0NEPjAxMjM0NTY3ODkwMTwvU09fQ0NDRD48TkdBWV9TSU5IPjE5ODAwMTAxMDAwMDwvTkdBWV9TSU5IPjxHSU9JX1RJTkg+MTwvR0lPSV9USU5IPjxOSE9NX01BVT5PPC9OSE9NX01BVT48TUFX...</NOIDUNGFILE>
</FILEHOSO>
```

#### Import/Export XML trong Django

**Import từ file XML:**
- **Upload file XML**: Sử dụng Django Form để tải lên file XML thông qua giao diện web
- **Xử lý file tải lên**: Sử dụng `request.FILES` để xử lý file tải lên từ form
- **Phân tích cấu trúc**: Sử dụng thư viện `xml.etree.ElementTree` để phân tích cấu trúc XML tổng thể (GIAMDINHHS, THONGTINDONVI, THONGTINHOSO)
- **Tìm thẻ FILEHOSO**: Tìm các thẻ FILEHOSO với LOAIHOSO tương ứng (XML1, XML2, ...)
- **Giải mã Base64**: Sử dụng thư viện `base64` của Python để giải mã nội dung Base64 trong thẻ NOIDUNGFILE
- **Chuyển đổi thành đối tượng Django**: Sử dụng hàm `parse_xml_to_model` để chuyển đổi chuỗi XML thành đối tượng Django model
- **Ánh xạ dữ liệu**: Ánh xạ các giá trị từ các thẻ XML vào các trường tương ứng của Django model
- **Lưu vào cơ sở dữ liệu**: Sử dụng phương thức `save()` của Django ORM để lưu đối tượng model vào cơ sở dữ liệu
- **Các số liệu tự sinh khi tạo hồ sơ XML `ngayTao`: ngày tạo mới, `ngayChinhSua`: ngày chỉnh sửa XML , `trangThaiGuiBHXH`: trạng thái gửi BHXH (mặc định là 0: chưa gửi, 1: đã gửi), `maTinh`: mã tỉnh (lấy từ vị trí thứ 4,5 của `maTheBHYT`)

**Export ra file XML:**
- **Truy vấn dữ liệu**: Sử dụng Django ORM để truy vấn dữ liệu từ cơ sở dữ liệu
- **Tạo đối tượng XML**: Sử dụng hàm `export_model_to_xml` để chuyển đổi đối tượng Django model thành chuỗi XML
- **Tạo cấu trúc XML**: Sử dụng `xml.etree.ElementTree` để tạo cấu trúc XML với các thẻ TONG_HOP và các thẻ con tương ứng
- **Điền giá trị**: Điền các giá trị từ model vào các thẻ XML tương ứng
- **Mã hóa Base64**: Sử dụng thư viện `base64` để mã hóa chuỗi XML thành chuỗi Base64
- **Đóng gói trong FILEHOSO**: Đóng gói chuỗi Base64 trong cấu trúc FILEHOSO
- **Tạo file XML hoàn chỉnh**: Tạo file XML hoàn chỉnh với cấu trúc GIAMDINHHS và các thành phần con
- **Phản hồi HTTP**: Sử dụng `HttpResponse` với Content-Type phù hợp để tải xuống file XML

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `maLK`                    | Mã liên kết                          | String          | 100          | ✅       |
| `maBN`                    | Mã bệnh nhân                         | String          | 100          | ✅       |
| `hoTen`                   | Họ và tên bệnh nhân                  | String          | 255          | ✅       |
| `soCCCD`                  | Số căn cước công dân                 | String          | n            | ❌       |
| `ngaySinh`                | Ngày sinh (yyyyMMddHHmm)             | String          | 12           | ❌       |
| `gioiTinh`                | Giới tính (1: Nam, 2: Nữ, 3: Chưa xác định)| Integer         | 1            | ❌       |
| `nhomMau`                 | Nhóm máu                             | String          | 10           | ❌       |
| `maQuocTich`              | Mã quốc tịch                         | String          | 10           | ❌       |
| `maDanToc`                | Mã dân tộc                           | String          | 10           | ❌       |
| `maNgheNghiep`            | Mã nghề nghiệp                       | String          | 10           | ❌       |
| `diaChi`                  | Địa chỉ                              | String          | 255          | ❌       |
| `maTinhCuTru`             | Mã tỉnh cư trú                       | String          | 10           | ❌       |
| `maHuyenCuTru`            | Mã huyện cư trú                      | String          | 10           | ❌       |
| `maXaCuTru`               | Mã xã cư trú                         | String          | 10           | ❌       |
| `dienThoai`               | Số điện thoại                        | String          | 20           | ❌       |
| `maTheBHYT`               | Mã thẻ BHYT                          | String          | 255          | ❌       |
| `maDKBD`                  | Mã đăng ký KCB ban đầu               | String          | 255          | ❌       |
| `gtTheTu`                 | Giá trị thẻ từ (yyyyMMddHHmm)        | String          | 255          | ❌       |
| `gtTheDen`                | Giá trị thẻ đến (yyyyMMddHHmm)       | String          | 255          | ❌       |
| `ngayMienCCT`             | Ngày miễn cùng chi trả               | String          | 12           | ❌       |
| `lyDoVV`                  | Lý do vào viện                       | String          | n            | ❌       |
| `lyDoVNT`                 | Lý do vào nội trú                    | String          | n            | ❌       |
| `maLyDoVNT`               | Mã lý do vào nội trú                 | String          | 10           | ❌       |
| `chanDoanVao`             | Chẩn đoán vào                        | String          | n            | ❌       |
| `chanDoanRV`              | Chẩn đoán ra viện                    | String          | n            | ❌       |
| `maBenhChinh`             | Mã bệnh chính                        | String          | 10           | ❌       |
| `maBenhKT`                | Mã bệnh kèm theo                     | String          | 255          | ❌       |
| `maBenhYHCT`              | Mã bệnh y học cổ truyền              | String          | 255          | ❌       |
| `maPTTTQT`                | Mã phẫu thuật thủ thuật quốc tế      | String          | 255           | ❌       |
| `maDoiTuongKCB`           | Mã đối tượng KCB                     | String          | 10           | ❌       |
| `maNoiDi`                 | Mã nơi đi                            | String          | 10           | ❌       |
| `maNoiDen`                | Mã nơi đến                           | String          | 10           | ❌       |
| `maTaiNan`                | Mã tai nạn                           | Integer         | 1            | ❌       |
| `ngayVao`                 | Ngày vào (yyyyMMddHHmm)              | String          | 12           | ❌       |
| `ngayVaoNoiTru`           | Ngày vào nội trú (yyyyMMddHHmm)      | String          | 12           | ❌       |
| `ngayRa`                  | Ngày ra (yyyyMMddHHmm)               | String          | 12           | ❌       |
| `giayChuyenTuyen`         | Giấy chuyển tuyến                    | String          | 50           | ❌       |
| `soNgayDtri`              | Số ngày điều trị                     | Integer         | 5            | ❌       |
| `ppDieuTri`               | Phương pháp điều trị                 | String          | n            | ❌       |
| `ketQuaDtri`              | Kết quả điều trị                     | Integer         | 1            | ❌       |
| `maLoaiRV`                | Mã loại ra viện                      | Integer         | 1            | ❌       |
| `ghiChu`                  | Ghi chú                              | String          | n            | ❌       |
| `ngayTToan`               | Ngày thanh toán (yyyyMMddHHmm)       | String          | 12           | ❌       |
| `tThuoc`                  | Tổng chi thuốc                       | Float           | -            | ❌       |
| `tVTYT`                   | Tổng chi vật tư y tế                 | Float           | -            | ❌       |
| `tTongChiBV`              | Tổng chi bệnh viện                   | Float           | -            | ❌       |
| `tTongChiBH`              | Tổng chi bảo hiểm                    | Float           | -            | ❌       |
| `tBNTT`                   | Bệnh nhân thanh toán                 | Float           | -            | ❌       |
| `tBNCCT`                  | Bệnh nhân cùng chi trả               | Float           | -            | ❌       |
| `tBHTT`                   | Bảo hiểm thanh toán                  | Float           | -            | ❌       |
| `tNguonKhac`              | Nguồn khác                           | Float           | -            | ❌       |
| `tBHTTGDV`                | BHTT giá dịch vụ                     | Float           | -            | ❌       |
| `namQT`                   | Năm quyết toán                       | Integer         | 4            | ❌       |
| `thangQT`                 | Tháng quyết toán                     | Integer         | 2            | ❌       |
| `maLoaiKCB`               | Mã loại KCB                          | String          | 10           | ❌       |
| `maKhoa`                  | Mã khoa                              | String          | 100          | ❌       |
| `maCSKCB`                 | Mã cơ sở KCB                         | String          | 10           | ❌       |
| `maKhuVuc`                | Mã khu vực                           | String          | 10           | ❌       |
| `canNang`                 | Cân nặng                             | String          | 10           | ❌       |
| `canNangCon`              | Cân nặng con                         | String          | 100          | ❌       |
| `namNamLienTuc`           | Năm năm liên tục                     | String          | 10           | ❌       |
| `ngayTaiKham`             | Ngày tái khám (yyyyMMddHHmm)         | String          | 12           | ❌       |
| `maHSBA`                  | Mã hồ sơ bệnh án                     | String          | 255          | ❌       |
| `maTTDV`                  | Mã trung tâm dịch vụ                 | String          | 255          | ❌       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |
| `ngayTao`                 | Ngày tạo (yyyyMMddHHmm)              | String          | 12           | ❌       |
| `ngayChinhSua`            | Ngày chỉnh sửa (yyyyMMddHHmm)        | String          | 12           | ❌       |
| `trangThaiGuiBHXH`        | Trạng thái gửi BHXH                  | SmallInteger    | 1            | ❌       |
| `maTinh`                  | Mã tỉnh                              | Integer         | 5            | ❌       |

### 2. XML2Model - Thông tin về thuốc

Bảng này lưu trữ thông tin về thuốc được sử dụng trong quá trình điều trị, bao gồm thông tin về loại thuốc, số lượng, đơn giá và các thông tin thanh toán.

#### Định dạng XML

Khi xuất ra file XML, dữ liệu XML2Model được chuyển đổi theo cấu trúc sau:

------Cấu trúc XML sau khi decode 
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<CHITIEU_CHITIET_THUOC>
  <DSACH_CHI_TIET_THUOC>
    <CHI_TIET_THUOC>
        <MA_LK>21016900140520250721</MA_LK>
        <STT>1</STT>
        <MA_THUOC>40.496</MA_THUOC>
        <MA_PP_CHEBIEN />
        <MA_CSKCB_THUOC />
        <MA_NHOM>4</MA_NHOM>
        <TEN_THUOC>Captopril</TEN_THUOC>
        <DON_VI_TINH>Viên</DON_VI_TINH>
        <HAM_LUONG>25mg</HAM_LUONG>
        <DUONG_DUNG>1.01</DUONG_DUNG>
        <DANG_BAO_CHE />
        <LIEU_DUNG>Ngày Uống 1 Lần, Lần 1 Viên uống sáng</LIEU_DUNG>
        <CACH_DUNG>Ngày Uống 1 Lần, Lần 1 Viên uống sáng</CACH_DUNG>
        <SO_DANG_KY>VD-32847-19</SO_DANG_KY>
        <TT_THAU>1192/QĐ-SYT;G1;N4;2022</TT_THAU>
        <PHAM_VI>1</PHAM_VI>
        <TYLE_TT_BH>100</TYLE_TT_BH>
        <SO_LUONG>10.00</SO_LUONG>
        <DON_GIA>108.00</DON_GIA>
        <THANH_TIEN_BV>1080.00</THANH_TIEN_BV>
        <THANH_TIEN_BH>1080.00</THANH_TIEN_BH>
        <T_NGUONKHAC_NSNN>0</T_NGUONKHAC_NSNN>
        <T_NGUONKHAC_VTNN>0</T_NGUONKHAC_VTNN>
        <T_NGUONKHAC_VTTN>0</T_NGUONKHAC_VTTN>
        <T_NGUONKHAC_CL>0.00</T_NGUONKHAC_CL>
        <T_NGUONKHAC>0.00</T_NGUONKHAC>
        <MUC_HUONG>100</MUC_HUONG>
        <T_BNTT>0.00</T_BNTT>
        <T_BNCCT>0.00</T_BNCCT>
        <T_BHTT>1080.00</T_BHTT>
        <MA_KHOA>K14</MA_KHOA>
        <MA_BAC_SI>160032/CCHN-BQP</MA_BAC_SI>
        <MA_DICH_VU />
        <NGAY_YL>202505200800</NGAY_YL>
        <NGAY_TH_YL>202505201359</NGAY_TH_YL>
        <MA_PTTT>1</MA_PTTT>
        <NGUON_CTRA>1</NGUON_CTRA>
        <VET_THUONG_TP />
        <DU_PHONG />
    </CHI_TIET_THUOC>
    ---- các DSACH_THUOC khác
  <CHITIEU_CHITIET_THUOC>
<DSACH_CHI_TIET_THUOC>
----------

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `maLK`                    | Mã liên kết                          | String          | 100          | ✅       |
| `stt`                     | Số thứ tự                            | Integer         | 10           | ✅       |
| `maThuoc`                 | Mã thuốc                             | String          | 255          | ✅       |
| `maPPCheBien`             | Mã phương pháp chế biến              | String          | 255          | ✅       |
| `maCSKCBThuoc`            | Mã CSKCB của thuốc                   | String          | 100          | ✅       |
| `maNhom`                  | Mã nhóm thuốc                        | Integer         | 2            | ✅       |
| `tenThuoc`                | Tên thuốc                            | String          | 1024         | ✅       |
| `donViTinh`               | Đơn vị tính                          | String          | 50           | ❌       |
| `hamLuong`                | Hàm lượng                            | String          | 1024         | ❌       |
| `duongDung`               | Đường dùng                           | String          | 4            | ❌       |
| `dangBaoChe`              | Dạng bào chế                         | String          | 1024         | ❌       |
| `lieuDung`                | Liều dùng                            | String          | 1024         | ❌       |
| `soDangKy`                | Số đăng ký                           | String          | 255          | ❌       |
| `ttThau`                  | Thông tin thầu                       | String          | 50           | ❌       |
| `phamVi`                  | Phạm vi                              | Integer         | 1            | ❌       |
| `tyLeTTBH`                | Tỷ lệ thanh toán BHYT                | Integer         | 3            | ❌       |
| `soLuong`                 | Số lượng                             | Float           | -            | ✅       |
| `donGia`                  | Đơn giá                              | Float           | -            | ✅       |
| `tNguonKhacNSNN`          | Tổng nguồn từ ngân sách nhà nước     | Float           | -            | ✅       |
| `tNguonKhacVTNN`          | Tồng nguồn từ nước ngoài             | Float           | -            | ✅       |
| `tNguonKhacVTTN`          | Tồng nguồn từ trong nước             | Float           | -            | ✅       |
| `tNguonKhacCL`            | Các nguồn còn lại                    | Float           | -            | ✅       |
| `tNguonKhac`              | Nguồn khác                           | Float           | -            | ✅       |
| `mucHuong`                | Mức hưởng                            | Float           | -            | ❌       |
| `tNguonKhac`              | Thanh toán nguồn khác                | Float           | -            | ❌       |
| `tBNTT`                   | Bệnh nhân thanh toán                 | Float           | -            | ❌       |
| `tBNCCT`                  | Bệnh nhân cùng chi trả               | Float           | -            | ❌       |
| `tBHTT`                   | Bảo hiểm thanh toán                  | Float           | -            | ❌       |
| `maKhoa`                  | Mã khoa                              | String          | 50           | ❌       |
| `maBacSi`                 | Mã bác sĩ                            | String          | 255          | ❌       |
| `maDichVu`                | Mã Dịch vụ                           | String          | 255          | ❌       |
| `ngayYL`                  | Ngày y lệnh (yyyyMMddHHmm)           | String          | 12           | ❌       |
| `ngayTHYL`                | Ngày thực hành y lệnh (yyyyMMddHHmm) | String          | 12           | ❌       |
| `maPTTT`                  | Mã Phương thức thanh toán            | Integer         | 1            | ❌       |
| `nguonCTra`               | Nguồn chi trả                        | Integer         | 1            | ❌       |
| `vetThuongTP`             | Vết thương tái phát                  | String          | 1            | ❌       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |

### 3. XML3Model -  Chỉ tiêu chi tiết dịch vụ kỹ thuật và vật tư y tế

Bảng này lưu trữ  Chỉ tiêu chi tiết dịch vụ kỹ thuật và vật tư y tế được sử dụng trong quá trình điều trị của bệnh nhân.

#### Định dạng XML

Khi xuất ra file XML, dữ liệu XML3Model được chuyển đổi theo cấu trúc sau:

```xml
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<CHITIEU_CHITIET_DVKT_VTYT>
  <DSACH_CHI_TIET_DVKT>
    <CHI_TIET_DVKT>
      <MA_LK>21016900140520250721</MA_LK>
      <STT>1</STT>
      <MA_DICH_VU>K14.1910</MA_DICH_VU>
      <MA_PTTT_QT />
      <MA_VAT_TU />
      <MA_NHOM>15</MA_NHOM>
      <GOI_VTYT />
      <TEN_VAT_TU />
      <TEN_DICH_VU>Giường Nội khoa loại 1 Hạng I - Khoa Thần kinh</TEN_DICH_VU>
      <MA_XANG_DAU />
      <DON_VI_TINH>Lần</DON_VI_TINH>
      <PHAM_VI>1</PHAM_VI>
      <SO_LUONG>1.00</SO_LUONG>
      <DON_GIA_BV>305500.00</DON_GIA_BV>
      <DON_GIA_BH>305500.00</DON_GIA_BH>
      <TT_THAU />
      <TYLE_TT_DV>100</TYLE_TT_DV>
      <TYLE_TT_BH>100</TYLE_TT_BH>
      <THANH_TIEN_BV>305500.00</THANH_TIEN_BV>
      <THANH_TIEN_BH>305500.00</THANH_TIEN_BH>
      <T_TRANTT />
      <MUC_HUONG>100</MUC_HUONG>
      <T_NGUONKHAC_NSNN>0</T_NGUONKHAC_NSNN>
      <T_NGUONKHAC_VTNN>0</T_NGUONKHAC_VTNN>
      <T_NGUONKHAC_VTTN>0</T_NGUONKHAC_VTTN>
      <T_NGUONKHAC_CL>0.00</T_NGUONKHAC_CL>
      <T_NGUONKHAC>0.00</T_NGUONKHAC>
      <T_BNTT>0.00</T_BNTT>
      <T_BNCCT>0.00</T_BNCCT>
      <T_BHTT>305500.00</T_BHTT>
      <MA_KHOA>K14</MA_KHOA>
      <MA_GIUONG>H001</MA_GIUONG>
      <MA_BAC_SI>160032/CCHN-BQP</MA_BAC_SI>
      <NGUOI_THUC_HIEN />
      <MA_BENH>M54.4;K21;R07.3;R51;I10</MA_BENH>
      <MA_BENH_YHCT />
      <NGAY_YL>202505190914</NGAY_YL>
      <NGAY_TH_YL />
      <NGAY_KQ>202505190914</NGAY_KQ>
      <MA_PTTT>1</MA_PTTT>
      <VET_THUONG_TP />
      <PP_VO_CAM />
      <VI_TRI_TH_DVKT />
      <MA_MAY />
      <MA_HIEU_SP />
      <TAI_SU_DUNG />
      <DU_PHONG />
    </CHI_TIET_DVKT>
    ---- các DSACH_CHI_TIET_DVKT khác
  </DSACH_CHI_TIET_DVKT>
</CHITIEU_CHITIET_DVKT_VTYT>
```

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `maLK`                    | Mã liên kết                          | String          | 100          | ✅       |
| `stt`                     | Số thứ tự                            | Integer         | 10           | ✅       |
| `maDichVu`                | Ghi mã DVKT/Khám/Giường              | String          | 100          | ✅       |
| `maPTTTQT`                | Ghi mã phẫu thuật thủ thuật quốc tế  | String          | 255          | ❌       |
| `maVatTu`                 | Mã vật tư y tế                       | String          | 255          | ✅       |
| `maNhom`                  | Mã nhóm theo chi phí                 | Integer         | 2            | ❌       |
| `goiVTYT`                 | Gói VTYT trong một lần sử dụng DVKT  | String          | 10           | ❌       |
| `tenVatTu`                | Tên vật tư y tế                      | String          | 1024         | ❌       |
| `tenDichVu`               | Tên DVKT/Khám/Giường                 | String          | 1024         | ❌       |
| `maXangDau`               | Ghi mã loại xăng dầu                 | String          | 20           | ❌       |
| `donViTinh`               | Đơn vị tính                          | String          | 50           | ❌       |
| `phamVi`                  | Phạm vi                              | Integer         | 1            | ❌       |
| `soLuong`                 | Số lượng                             | Float           | -            | ❌       |
| `donGiaBV`                | Đơn giá bệnh viện                    | Float           | -            | ❌       |
| `donGiaBH`                | Đơn giá bảo hiểm                     | Float           | -            | ❌       |
| `tyLeTT`                  | Tỷ lệ thanh toán                     | Float           | -            | ❌       |
| `thanhTienBV`             | Thành tiền bệnh viện                 | Float           | -            | ❌       |
| `thanhTienBH`             | Thành tiền bảo hiểm                  | Float           | -            | ❌       |
| `ttThau`                  | Thông tin thầu                       | String          | 50           | ❌       |
| `tyLeTTDV`                | Tỷ lệ thanh toán DVKT                | Integer         | 3            | ❌       |
| `tyLeTTBH`                | Tỷ lệ thanh toán BHYT                | Integer         | 3            | ❌       |
| `tTranTT`                 | Trần thanh toán                      | Float           | -            | ❌       |
| `mucHuong`                | Mức hưởng                            | Integer         | 3            | ❌       |
| `tNguonKhacNSNN`          | Tổng nguồn từ ngân sách nhà nước     | Float           | -            | ❌       |
| `tNguonKhacVTNN`          | Tồng nguồn từ nước ngoài             | Float           | -            | ❌       |
| `tNguonKhacVTTN`          | Tồng nguồn từ trong nước             | Float           | -            | ❌       |
| `tNguonKhacCL`            | Các nguồn còn lại                    | Float           | -            | ❌       |
| `tNguonKhac`              | Thanh toán nguồn khác                | Float           | -            | ❌       |
| `tBNTT`                   | Bệnh nhân thanh toán                 | Float           | -            | ❌       |
| `tBNCCT`                  | Bệnh nhân cùng chi trả               | Float           | -            | ❌       |
| `tBHTT`                   | Bảo hiểm thanh toán                  | Float           | -            | ❌       |
| `maKhoa`                  | Mã khoa                              | String          | 50           | ❌       |
| `maGiuong`                | Mã giường                            | String          | 50           | ❌       |
| `maBacSi`                 | Mã bác sĩ                            | String          | 255          | ❌       |
| `maBenh`                  | Mã bệnh                              | String          | 255          | ❌       |
| `maBenhYHCT`              | Mã bệnh YHCT                         | String          | 255          | ❌       |
| `ngayYL`                  | Ngày y lệnh (yyyyMMddHHmm)           | String          | 12           | ❌       |
| `ngayTHYL`                | Ngày thực hành y lệnh (yyyyMMddHHmm) | String          | 12           | ❌       |
| `ngayKQ`                  | Ngày kết quả (yyyyMMddHHmm)          | String          | 12           | ❌       |
| `maPTTT`                  | Mã Phương thức thanh toán            | Integer         | 1            | ❌       |
| `vetThuongTP`             | Vết thương tái phát                  | String          | 1            | ❌       |
| `ppVoCam`                 | Phương pháp vô cảm                   | String          | 3            | ❌       |
| `viTriThDVKT`             | Vị trí thực hiện DVKT                | String          | 255          | ❌       |
| `maMay`                   | Mã máy                               | String          | 1024         | ❌       |
| `maHieuSP`                | Mã hiệu sản phẩm                     | String          | 255          | ❌       |
| `taiSuDung`               | Tài sử dụng                          | String          | 1            | ❌       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |

### 4. XML4Model - Chỉ tiêu chi tiết dịch vụ cận lâm sàng

Bảng này lưu trữ thông tin về các Chỉ tiêu chi tiết dịch vụ cận lâm sàng của bệnh nhân.

#### Định dạng XML

Khi xuất ra file XML, dữ liệu XML4Model được chuyển đổi theo cấu trúc sau:

```xml
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<CHITIEU_CHITIET_DICHVUCANLAMSANG>
  <DSACH_CHI_TIET_CLS>
    <CHI_TIET_CLS>
      <MA_LK>21016900140520250721</MA_LK>
      <STT>1</STT>
      <MA_DICH_VU>17.0007.0234</MA_DICH_VU>
      <MA_CHI_SO />
      <TEN_CHI_SO />
      <GIA_TRI />
      <DON_VI_DO />
      <MO_TA>chuẩn bị bệnh nhân, đặt bn ở tư thế thuận lợi, giải thích, bộc lộ vùng điều trị, đặt cực điện theo y lệnh, thời gian 20 phút</MO_TA>
      <KET_LUAN>an toàn</KET_LUAN>
      <NGAY_KQ>202505180915</NGAY_KQ>
      <MA_BS_DOC_KQ>2115/KT-CCHN</MA_BS_DOC_KQ>
      <DU_PHONG />
    </CHI_TIET_CLS>
    ---- các DSACH_CHI_TIET_CLS khác
  </DSACH_CHI_TIET_CLS>
</CHITIEU_CHITIET_DICHVUCANLAMSANG>
```

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `maLK`                    | Mã liên kết                          | String          | 100          | ✅       |
| `stt`                     | Số thứ tự                            | Integer         | 10           | ✅       |
| `maDichVu`                | Mã dịch vụ                           | String          | 100          | ✅       |
| `maChiSo`                 | Mã chỉ số                            | String          | 255          | ❌       |
| `tenChiSo`                | Tên chỉ số                           | String          | 255          | ❌       |
| `giaTri`                  | Giá trị                              | String          | 255          | ❌       |
| `donViDo`                 | Đơn vị đo                            | String          | 50           | ❌       |
| `moTa`                    | Mô tả                                | String          | n            | ❌       |
| `ketLuan`                 | Kết luận                             | String          | n            | ❌       |
| `ngayKQ`                  | Ngày kết quả (yyyyMMddHHmm)          | String          | 12           | ❌       |
| `maBSDocKQ`               | Mã bác sĩ đọc kết quả                | String          | 255          | ❌       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |

### 5. XML5Model - Chỉ tiêu chi tiết diễn biến lâm sàng

Bảng này lưu trữ Chỉ tiêu chi tiết diễn biến lâm sàng của bệnh nhân trong quá trình điều trị, bao gồm thông tin về giai đoạn bệnh, hội chẩn và phẫu thuật.

#### Định dạng XML

Khi xuất ra file XML, dữ liệu XML5Model được chuyển đổi theo cấu trúc sau:

```xml
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<CHITIEU_CHITIET_DIENBIENLAMSANG>
  <DSACH_CHI_TIET_DIEN_BIEN_BENH>
    <CHI_TIET_DIEN_BIEN_BENH>
      <MA_LK>21016900140520250721</MA_LK>
      <STT>1</STT>
      <DIEN_BIEN_LS>Viêm khớp</DIEN_BIEN_LS>
      <GIAI_DOAN_BENH />
      <HOI_CHAN />
      <PHAU_THUAT />
      <THOI_DIEM_DBLS>202505140702</THOI_DIEM_DBLS>
      <NGUOI_THUC_HIEN>QY160009/CCHN-BQP</NGUOI_THUC_HIEN>
      <DU_PHONG />
    </CHI_TIET_DIEN_BIEN_BENH>
    ---- các DSACH_CHI_TIET_DIEN_BIEN_BENH khác
  </DSACH_CHI_TIET_DIEN_BIEN_BENH>
</CHITIEU_CHITIET_DIENBIENLAMSANG>
```

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `maLK`                    | Mã liên kết                          | String          | 100          | ✅       |
| `stt`                     | Số thứ tự                            | Integer         | 10           | ✅       |
| `dienBienLS`              | Diễn biến lâm sàng                   | String          | n            | ❌       |
| `giaiDoanBenh`            | Giai đoạn bệnh                       | String          | n            | ❌       |
| `hoiChan`                 | Hội chẩn                             | String          | n            | ❌       |
| `phauThuat`               | Phẫu thuật                           | String          | n            | ❌       |
| `thoiDiemDBLS`            | Thời điểm diễn biến (yyyyMMddHHmm)   | String          | 12           | ❌       |
| `nguoiThucHien`           | Người thực hiện                      | String          | 255          | ❌       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |

### 6. XML6Model - Chỉ tiêu hồ sơ bệnh án chăm sóc và điều trị HIV/AIDS

Bảng này lưu trữ Chỉ tiêu hồ sơ bệnh án chăm sóc và điều trị HIV/AIDS của bệnh nhân, bao gồm thông tin cá nhân, thông tin HIV và thông tin điều trị.

#### Định dạng XML
Khi xuất ra file XML, dữ liệu XML6Model được chuyển đổi theo cấu trúc sau:

```xml
<CHI_TIEU_HO_SO_BENH_AN_CHAM_SOC_VA_DIEU_TRI_HIV_AIDS>
  <DSACH_HO_SO_BENH_AN_CHAM_SOC_VA_DIEU_TRI_HIV_AIDS>
    <HO_SO_BENH_AN_CHAM_SOC_VA_DIEU_TRI_HIV_AIDS>
      <MA_LK></MA_LK>
      <MA_THE_BHYT></MA_THE_BHYT>
      <SO_CCCD></SO_CCCD>
      <NGAY_SINH></NGAY_SINH>
      <GIOI_TINH></GIOI_TINH>
      <DIA_CHI></DIA_CHI>
      <MATINH_CU_TRU></MATINH_CU_TRU>
      <MAHUYEN_CU_TRU></MAHUYEN_CU_TRU>
      <MAXA_CU_TRU></MAXA_CU_TRU>
      <NGAYKD_HIV></NGAYKD_HIV>
      <NOI_LAY_MAU_XN></NOI_LAY_MAU_XN>
      <NOI_XN_KD></NOI_XN_KD>
      <NOI_BDDT_ARV></NOI_BDDT_ARV>
      <BDDT_ARV></BDDT_ARV>
      <MA_PHAC_DO_DIEU_TRI_BD></MA_PHAC_DO_DIEU_TRI_BD>
      <MA_BAC_PHAC_DO_BD></MA_BAC_PHAC_DO_BD>
      <MA_LYDO_DTRI></MA_LYDO_DTRI>
      <LOAI_DTRI_LAO></LOAI_DTRI_LAO>
      <SANG_LOC_LAO></SANG_LOC_LAO>
      <PHACDO_DTRI_LAO></PHACDO_DTRI_LAO>
      <NGAYBD_DTRI_LAO></NGAYBD_DTRI_LAO>
      <NGAYKT_DTRI_LAO></NGAYKT_DTRI_LAO>
      <KQ_DTRI_LAO></KQ_DTRI_LAO>
      <MA_LYDO_XNTL_VR></MA_LYDO_XNTL_VR>
      <NGAY_XN_TLVR></NGAY_XN_TLVR>
      <KQ_XNTL_VR></KQ_XNTL_VR>
      <NGAY_KQ_XN_TLVR></NGAY_KQ_XN_TLVR>
      <MA_LOAI_BN></MA_LOAI_BN>
      <GIAI_DOAN_LAM_SANG></GIAI_DOAN_LAM_SANG>
      <NHOM_DOI_TUONG></NHOM_DOI_TUONG>
      <MA_TINH_TRANG_DK></MA_TINH_TRANG_DK>
      <LAN_XN_PCR></LAN_XN_PCR>
      <NGAY_XN_PCR></NGAY_XN_PCR>
      <NGAY_KQ_XN_PCR></NGAY_KQ_XN_PCR>
      <MA_KQ_XN_PCR></MA_KQ_XN_PCR>
      <NGAY_NHAN_TT_MANG_THAI></NGAY_NHAN_TT_MANG_THAI>
      <NGAY_BAT_DAU_DT_CTX></NGAY_BAT_DAU_DT_CTX>
      <MA_XU_TRI></MA_XU_TRI>
      <NGAY_BAT_DAU_XU_TRI></NGAY_BAT_DAU_XU_TRI>
      <NGAY_KET_THUC_XU_TRI></NGAY_KET_THUC_XU_TRI>
      <MA_PHAC_DO_DIEU_TRI></MA_PHAC_DO_DIEU_TRI>
      <MA_BAC_PHAC_DO></MA_BAC_PHAC_DO>
      <SO_NGAY_CAP_THUOC_ARV></SO_NGAY_CAP_THUOC_ARV>
      <NGAY_CHUYEN_PHAC_DO></NGAY_CHUYEN_PHAC_DO>
      <LY_DO_CHUYEN_PHAC_DO></LY_DO_CHUYEN_PHAC_DO>
      <MA_CSKCB></MA_CSKCB>
      <DU_PHONG></DU_PHONG>
    </HO_SO_BENH_AN_CHAM_SOC_VA_DIEU_TRI_HIV_AIDS>
    ---- các DSACH_HO_SO_BENH_AN_CHAM_SOC_VA_DIEU_TRI_HIV_AIDS khác
  </DSACH_HO_SO_BENH_AN_CHAM_SOC_VA_DIEU_TRI_HIV_AIDS>
</CHI_TIEU_HO_SO_BENH_AN_CHAM_SOC_VA_DIEU_TRI_HIV_AIDS>
```

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `maLK`                    | Mã liên kết                          | String          | 100          | ✅       |
| `maTheBHYT`               | Mã thẻ BHYT                          | String          | n            | ✅       |
| `soCCCD`                  | Số căn cước công dân                 | String          | n            | ❌       |
| `ngaySinh`                | Ngày sinh (yyyyMMddHHmm)             | String          | 12           | ❌       |
| `gioiTinh`                | Giới tính (1: Nam, 2: Nữ)            | Integer         | 1            | ❌       |
| `diaChi`                  | Địa chỉ                              | String          | 1024         | ❌       |
| `maTinhCuTru`             | Mã tỉnh cư trú                       | String          | 10           | ❌       |
| `maHuyenCuTru`            | Mã huyện cư trú                      | String          | 10           | ❌       |
| `maXaCuTru`               | Mã xã cư trú                         | String          | 10           | ❌       |
| `ngayKDHIV`               | Ngày khẳng định HIV (yyyyMMdd)       | String          | 8            | ❌       |
| `noiLayMauXN`             | Nơi lấy mẫu xét nghiệm               | String          | 100          | ❌       |
| `noiXNKD`                 | Nơi xét nghiệm khẳng định            | String          | 100          | ❌       |
| `noiBDDTARV`              | Nơi bắt đầu điều trị ARV             | String          | 100          | ❌       |
| `maPhaDoDieuTriBD`        | Mã phác đồ điều trị ban đầu          | String          | 200          | ❌       |
| `maBacPhacDoBD`           | Mã bậc phác đồ ban đầu               | Integer         | 1            | ❌       |
| `maLydoDtri`              | Mã lý do điều trị                    | Integer         | 1            | ❌       |
| `loaiDtriLao`             | Loại điều trị lao                    | Integer         | 1            | ❌       |
| `sangLocLao`              | Sàng lọc lao                         | Integer         | 1            | ❌       |
| `phacDoDtriLao`           | Phác đồ điều trị lao                 | Integer         | 2            | ❌       |
| `ngayBDDTriLao`           | Ngày bắt đầu điều trị lao (yyyyMMdd) | String          | 8            | ❌       |
| `ngayKTDTriLao`           | Ngày kết thúc điều trị lao (yyyyMMdd)| String          | 8            | ❌       |
| `ketQuaDTriLao`           | Kết quả điều trị lao                 | Integer         | 1            | ❌       |
| `maLydoXNTLVR`            | Mã lý do xét nghiệm TLVR             | Integer         | 1            | ❌       |
| `ngayXNTLVR`              | Ngày xét nghiệm TLVR (yyyyMMdd)      | String          | 8            | ❌       |
| `kqXNTLVR`                | Kết quả xét nghiệm TLVR              | Integer         | 1            | ❌       |
| `ngayKQXNTLVR`            | Ngày kết quả xét nghiệm TLVR (yyyyMMdd) | String       | 8            | ❌       |
| `maLoaiBN`                | Mã loại bệnh nhân                    | Integer         | 1            | ❌       |
| `giaiDoanLamSang`         | Giải đoạn làm sáng                   | Integer         | 1            | ❌       |
| `nhomDoiTuong`            | Nhóm đối tượng                       | Integer         | 2            | ❌       |
| `maTinhTrangDK`           | Mã tình trạng đăng ký                | String          | 20           | ❌       |
| `lanXNPCR`                | Lần xét nghiệm PCR                   | Integer         | 1            | ❌       |
| `ngayXNPCR`               | Ngày xét nghiệm PCR (yyyyMMdd)       | String          | 8            | ❌       |
| `ngayKQXNPCR`             | Ngày kết quả xét nghiệm PCR (yyyyMMdd)| String         | 8            | ❌       |
| `maKQXNPCR`               | Mã kết quả xét nghiệm PCR            | Integer         | 1            | ❌       |
| `ngayNhanTTMangThai`      | Ngày nhận thông tin mang thai (yyyyMMdd) | String      | 8            | ❌       |
| `ngayBatDauDTCTX`         | Ngày bắt đầu điều trị ctx (yyyyMMdd) | String          | 8            | ❌       |
| `maXuTri`                 | Mã xử trí                            | Integer         | 1            | ❌       |
| `ngayBatDauXuTri`         | Ngày bắt đầu xử trí (yyyyMMdd)       | String          | 8            | ❌       |
| `ngayKetThucXuTri`        | Ngày kết thúc xử trí (yyyyMMdd)      | String          | 8            | ❌       |
| `maPhacDoDieuTri`         | Mã phác đồ điều trị                  | String          | 200          | ❌       |
| `maBacPhacDo`             | Mã bậc phác đồ                       | Integer         | 1            | ❌       |
| `soNgayCapThuocARV`       | Số ngày cấp thuốc ARV                | Integer         | 3            | ❌       |
| `ngayChuyenPhacDo`        | Ngày chuyển phác đồ (yyyyMMdd)       | String          | 8            | ❌       |
| `lyDoChuyenPhacDo`        | Lý do chuyển phác đồ                 | Integer         | 1            | ❌       |
| `maCSKCB`                 | Mã cơ sở khám chữa bệnh              | String          | 5            | ❌       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |

### 7. XML7Model - Chỉ tiêu dữ liệu giấy ra viện

Bảng này lưu trữ Chỉ tiêu dữ liệu giấy ra viện của bệnh nhân

#### Định dạng XML

Khi xuất ra file XML, dữ liệu XML7Model được chuyển đổi theo cấu trúc sau:

```xml
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<CHI_TIEU_DU_LIEU_GIAY_RA_VIEN>
  <MA_LK>21016900140520250721</MA_LK>
  <SO_LUU_TRU>25.004469</SO_LUU_TRU>
  <MA_YTE>21016900</MA_YTE>
  <MA_KHOA_RV>K14</MA_KHOA_RV>
  <NGAY_VAO>202505140702</NGAY_VAO>
  <NGAY_RA>202505200800</NGAY_RA>
  <MA_DINH_CHI_THAI>0</MA_DINH_CHI_THAI>
  <NGUYENNHAN_DINHCHI />
  <THOIGIAN_DINHCHI />
  <TUOI_THAI />
  <CHAN_DOAN_RV>U62.392.4: Đau lưng kèm đau dây thần kinh tọa</CHAN_DOAN_RV>
  <PP_DIEUTRI>Giảm đau, chống viêm, giãn cơ, sinh tố, tăng dẫn truyền thần kinh, hạ áp, vật lý trị liệu.</PP_DIEUTRI>
  <GHI_CHU>Ra viện và điều trị củng cố theo hướng dẫn, dùng thuốc theo đơn ngoại trú.</GHI_CHU>
  <MA_TTDV>6600502865</MA_TTDV>
  <MA_BS>6899502559</MA_BS>
  <TEN_BS>NGUYỄN ĐỨC TUẤN</TEN_BS>
  <NGAY_CT>20250520</NGAY_CT>
  <MA_CHA />
  <MA_ME />
  <MA_THE_TAM />
  <HO_TEN_CHA />
  <HO_TEN_ME />
  <SO_NGAY_NGHI />
  <NGOAITRU_TUNGAY />
  <NGOAITRU_DENNGAY />
  <DU_PHONG />
</CHI_TIEU_DU_LIEU_GIAY_RA_VIEN>
```

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `maLK`                    | Mã liên kết                          | String          | 100          | ✅       |
| `soLuuTru`                | Số lưu trữ                           | String          | 200          | ❌       |
| `maYTe`                   | Mã y tế                              | String          | 200          | ❌       |
| `maKhoaRV`                | Mã khoa ra viện                      | String          | 200          | ❌       |
| `ngayVao`                 | Ngày vào (yyyyMMddHHmm)              | String          | 12           | ❌       |
| `ngayRa`                  | Ngày ra (yyyyMMddHHmm)               | String          | 12           | ❌       |
| `maDinhChiThai`           | Mã định chỉ thai                     | Integer         | 1            | ❌       |
| `nguyenNhanDinhChi`       | Nguyên nhân định chỉ                 | String          | n            | ❌       |
| `thoiGianDinhChi`         | Thời gian định chỉ (yyyyMMddHHmm)    | String          | 12           | ❌       |
| `tuoiThai`                | Tuổi thai (tuần)                     | Integer         | 2            | ❌       |
| `chanDoanRV`              | Chẩn đoán ra viện                    | String          | 1500         | ❌       |
| `ppDieuTri`               | Phương pháp điều trị                 | String          | n            | ❌       |
| `ghiChu`                  | Ghi chú                              | String          | 1500         | ❌       |
| `maTTDV`                  | Mã trung tâm dịch vụ                 | String          | 255          | ❌       |
| `maBS`                    | Mã bác sĩ                            | String          | 255          | ❌       |
| `tenBS`                   | Tên bác sĩ                           | String          | 255          | ❌       |
| `ngayCT`                  | Ngày chứng từ (yyyyMMdd)             | String          | 8            | ❌       |
| `maCha`                   | Mã cha                               | String          | 50           | ❌       |
| `maMe`                    | Mã mẹ                                | String          | 50           | ❌       |
| `maTheTam`                | Mã thẻ tạm                           | String          | 15           | ❌       |
| `hoTenCha`                | Họ tên cha                           | String          | 255          | ❌       |
| `hoTenMe`                 | Họ tên mẹ                            | String          | 255          | ❌       |
| `soNgayNghi`              | Số ngày nghỉ                         | Integer         | 3            | ❌       |
| `ngoaiTruTuNgay`          | Ngoại trú từ ngày (yyyyMMdd)         | String          | 8            | ❌       |
| `ngoaiTruDenNgay`         | Ngoại trú đến ngày (yyyyMMdd)        | String          | 8            | ❌       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |

### 8. XML8Model - Chỉ tiêu dữ liệu tóm tắt hồ sơ bệnh án

Bảng này lưu trữ Chỉ tiêu dữ liệu tóm tắt hồ sơ bệnh án

#### Định dạng XML

Khi xuất ra file XML, dữ liệu XML8Model được chuyển đổi theo cấu trúc sau:

```xml
<CHI_TIEU_DU_LIEU_TOM_TAT_HO_SO_BENH_AN>
  <MA_LK></MA_LK>
  <MA_LOAI_KCB></MA_LOAI_KCB>
  <HO_TEN_CHA></HO_TEN_CHA>
  <HO_TEN_ME></HO_TEN_ME>
  <NGUOI_GIAM_HO></NGUOI_GIAM_HO>
  <DON_VI></DON_VI>
  <NGAY_VAO></NGAY_VAO>
  <NGAY_RA></NGAY_RA>
  <CHAN_DOAN_VAO></CHAN_DOAN_VAO>
  <CHAN_DOAN_RV></CHAN_DOAN_RV>
  <QT_BENHLY></QT_BENHLY>
  <TOMTAT_KQ></TOMTAT_KQ>
  <PP_DIEUTRI></PP_DIEUTRI>
  <NGAY_SINHCON></NGAY_SINHCON>
  <NGAY_CONCHET></NGAY_CONCHET>
  <SO_CONCHET></SO_CONCHET>
  <KET_QUA_DTRI></KET_QUA_DTRI>
  <GHI_CHU></GHI_CHU>
  <MA_TTDV></MA_TTDV>
  <NGAY_CT></NGAY_CT>
  <MA_THE_TAM></MA_THE_TAM>
  <DU_PHONG></DU_PHONG>
</CHI_TIEU_DU_LIEU_TOM_TAT_HO_SO_BENH_AN>
```

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `maLK`                    | Mã liên kết                          | String          | 50           | ✅       |
| `maLoaiKCB`               | Mã loại KCB                          | String          | 2            | ✅       |
| `hoTenCha`                | Họ tên cha                           | String          | 255          | ❌       |
| `hoTenMe`                 | Họ tên mẹ                            | String          | 255          | ❌       |
| `nguoiGiamHo`             | Người giám hộ                        | String          | 255          | ❌       |
| `donVi`                   | Đơn vị                               | String          | 1024         | ❌       |
| `ngayVao`                 | Ngày vào (yyyyMMddHHmm)              | String          | 12           | ❌       |
| `ngayRa`                  | Ngày ra (yyyyMMddHHmm)               | String          | 12           | ❌       |
| `chanDoanVao`             | Chẩn đoán vào                        | String          | n            | ❌       |
| `chanDoanRV`              | Chẩn đoán ra viện                    | String          | n            | ❌       |
| `qtBenhLy`                | Quá trình bệnh lý                    | String          | n            | ❌       |
| `tomtatKQ`                | Tóm tắt kết quả                      | String          | n            | ❌       |
| `ppDieuTri`               | Phương pháp điều trị                 | String          | n            | ❌       |
| `ngaySinhCon`             | Ngày sinh con (yyyyMMdd)             | String          | 8            | ❌       |
| `ngayConChet`             | Ngày con chết (yyyyMMdd)             | String          | 8            | ❌       |
| `soConChet`               | Số con chết                          | Integer         | 3            | ❌       |
| `ketQuaDtri`              | Kết quả điều trị                     | Integer         | 1            | ❌       |
| `ghiChu`                  | Ghi chú                              | String          | n            | ❌       |
| `maTTDV`                  | Mã trung tâm dịch vụ                 | String          | 255          | ❌       |
| `ngayCT`                  | Ngày chứng từ (yyyyMMdd)             | String          | 8            | ❌       |
| `maTheTam`                | Mã thẻ tạm                           | String          | 15           | ❌       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |

### 9. XML9Model - Chỉ tiêu dữ liệu giấy chứng sinh

Bảng này lưu trữ Chỉ tiêu dữ liệu giấy chứng sinh
#### Định dạng XML

Khi xuất ra file XML, dữ liệu XML9Model được chuyển đổi theo cấu trúc sau:

```xml
<CHI_TIEU_DU_LIEU_GIAY_CHUNG_SINH>
  <DSACH_GIAYCHUNGSINH>
    <DU_LIEU_GIAY_CHUNG_SINH>
      <MA_LK></MA_LK>
      <MA_BHXH_NND></MA_BHXH_NND>
      <MA_THE_NND></MA_THE_NND>
      <HO_TEN_NND></HO_TEN_NND>
      <NGAYSINH_NND></NGAYSINH_NND>
      <MA_DANTOC_NND></MA_DANTOC_NND>
      <SO_CCCD_NND></SO_CCCD_NND>
      <NGAYCAP_CCCD_NND></NGAYCAP_CCCD_NND>
      <NOICAP_CCCD_NND></NOICAP_CCCD_NND>
      <NOI_CU_TRU_NND></NOI_CU_TRU_NND>
      <MA_QUOCTICH></MA_QUOCTICH>
      <MATINH_CU_TRU></MATINH_CU_TRU>
      <MAHUYEN_CU_TRU></MAHUYEN_CU_TRU>
      <MAXA_CU_TRU></MAXA_CU_TRU>
      <HO_TEN_CHA></HO_TEN_CHA>
      <MA_THE_TAM></MA_THE_TAM>
      <HO_TEN_CON></HO_TEN_CON>
      <GIOI_TINH_CON></GIOI_TINH_CON>
      <SO_CON></SO_CON>
      <LAN_SINH></LAN_SINH>
      <SO_CON_SONG></SO_CON_SONG>
      <CAN_NANG_CON></CAN_NANG_CON>
      <NGAY_SINH_CON></NGAY_SINH_CON>
      <NOI_SINH_CON></NOI_SINH_CON>
      <TINH_TRANG_CON></TINH_TRANG_CON>
      <SINHCON_PHAUTHUAT></SINHCON_PHAUTHUAT>
      <SINHCON_DUOI32TUAN></SINHCON_DUOI32TUAN>
      <GHI_CHU></GHI_CHU>
      <NGUOI_DO_DE></NGUOI_DO_DE>
      <NGUOI_GHI_PHIEU></NGUOI_GHI_PHIEU>
      <NGAY_CT></NGAY_CT>
      <SO></SO>
      <QUYEN_SO></QUYEN_SO>
      <MA_TTDV></MA_TTDV>
      <DU_PHONG></DU_PHONG>
    </DU_LIEU_GIAY_CHUNG_SINH>
    ---- các DSACH_GIAYCHUNGSINH khác
  </DSACH_GIAYCHUNGSINH>
</CHI_TIEU_DU_LIEU_GIAY_CHUNG_SINH>
```

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `maLK`                    | Mã liên kết                          | String          | 100          | ✅       |
| `maBHXHNND`               | Mã BHXH người nhà                    | String          | 20           | ❌       |
| `maTheNND`                | Mã thẻ người nhà                     | String          | 20           | ❌       |
| `hoTenNND`                | Họ tên người nhà                     | String          | 255          | ❌       |
| `ngaySinhNND`             | Ngày sinh người nhà (yyyyMMdd)       | String          | 8            | ❌       |
| `maDanTocNND`             | Mã dân tộc người nhà                 | String          | 10           | ❌       |
| `soCCCDNND`               | Số CCCD người nhà                    | String          | n            | ❌       |
| `ngayCapCCCDNND`          | Ngày cấp CCCD người nhà (yyyyMMdd)   | String          | 8            | ❌       |
| `noiCapCCCDNND`           | Nơi cấp CCCD người nhà               | String          | 1024         | ❌       |
| `noiCuTruNND`             | Nơi cư trú người nhà                 | String          | 1024         | ❌       |
| `maQuocTich`              | Mã quốc tịch                         | String          | 10           | ❌       |
| `maTinhCuTru`             | Mã tỉnh cư trú                       | String          | 10           | ❌       |
| `maHuyenCuTru`            | Mã huyện cư trú                      | String          | 10           | ❌       |
| `maXaCuTru`               | Mã xã cư trú                         | String          | 10           | ❌       |
| `hoTenCha`                | Họ tên cha                           | String          | 255          | ❌       |
| `maTheTam`                | Mã thẻ tạm                           | String          | 15           | ❌       |
| `hoTenCon`                | Họ tên con                           | String          | 255          | ❌       |
| `gioiTinhCon`             | Giới tính con                        | Integer         | 1            | ❌       |
| `soCon`                   | Số con                               | Integer         | 3            | ❌       |
| `lanSinh`                 | Lần sinh                             | Integer         | 3            | ❌       |
| `soConSong`               | Số con sống                          | Integer         | 3            | ❌       |
| `canNangCon`              | Cân nặng con                         | Float           | 10           | ❌       |
| `ngaySinhCon`             | Ngày sinh con (yyyyMMddHHmm)         | String          | 12           | ❌       |
| `noiSinhCon`              | Nơi sinh con                         | String          | 1024         | ❌       |
| `tinhTrangCon`            | Tình trạng con                       | String          | n            | ❌       |
| `sinhConPhauThuat`        | Sinh con phẫu thuật                  | Integer         | 1            | ❌       |
| `sinhConDuoi32Tuan`       | Sinh con dưới 32 tuần                | Integer         | 1            | ❌       |
| `ghiChu`                  | Ghi chú                              | String          | n            | ❌       |
| `nguoiDoDe`               | Người đỡ đẻ                          | String          | 255          | ❌       |
| `nguoiGhiPhieu`           | Người ghi phiếu                      | String          | 255          | ❌       |
| `ngayCT`                  | Ngày chứng từ (yyyyMMdd)             | String          | 8            | ❌       |
| `so`                      | Số                                   | String          | 200          | ❌       |
| `quyenSo`                 | Quyển số                             | String          | 200          | ❌       |
| `maTTDV`                  | Mã trung tâm dịch vụ                 | String          | 255          | ❌       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |

### 10. XML10Model - Chỉ tiêu dữ liệu giấy chứng nhận nghỉ dưỡng thai

Bảng này lưu trữ thông tin về Chỉ tiêu dữ liệu giấy chứng nhận nghỉ dưỡng thai
#### Định dạng XML

Khi xuất ra file XML, dữ liệu XML10Model được chuyển đổi theo cấu trúc sau:

```xml
<CHI_TIEU_DU_LIEU_GIAY_NGHI_DUONG_THAI>
  <MA_LK></MA_LK>
  <SO_SERI></SO_SERI>
  <SO_CT></SO_CT>
  <SO_NGAY></SO_NGAY>
  <DON_VI></DON_VI>
  <CHAN_DOAN_RV></CHAN_DOAN_RV>
  <TU_NGAY></TU_NGAY>
  <DEN_NGAY></DEN_NGAY>
  <MA_TTDV></MA_TTDV>
  <TEN_BS></TEN_BS>
  <MA_BS></MA_BS>
  <NGAY_CT></NGAY_CT>
  <DU_PHONG></DU_PHONG>
</CHI_TIEU_DU_LIEU_GIAY_NGHI_DUONG_THAI>
```

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `maLK`                    | Mã liên kết                          | String          | 100          | ✅       |
| `soSeri`                  | Số seri                              | String          | 200          | ✅       |
| `soCT`                    | Số chứng từ                          | String          | 200          | ✅       |
| `soNgay`                  | Số ngày                              | Integer         | 3            | ✅       |
| `donVi`                   | Đơn vị                               | String          | 1024         | ❌       |
| `chanDoanRV`              | Chẩn đoán ra viện                    | String          | n            | ✅       |
| `tuNgay`                  | Từ ngày (yyyyMMdd)                   | String          | 8            | ✅       |
| `denNgay`                 | Đến ngày (yyyyMMdd)                  | String          | 8            | ✅       |
| `maTTDV`                  | Mã Thủ trưởng đơn vị                 | String          | 255          | ✅       |
| `maBS`                    | Mã bác sĩ                            | String          | 255          | ❌       |
| `tenBS`                   | Tên bác sĩ                           | String          | 255          | ❌       |
| `ngayCT`                  | Ngày chứng từ (yyyyMMd)              | String          | 8            | ✅       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |

### 11. XML11Model - Chỉ tiêu dữ liệu giấy chứng nhận nghỉ việc hưởng bảo hiểm xã hội

Bảng này lưu trữ thông tin về Chỉ tiêu dữ liệu giấy chứng nhận nghỉ việc hưởng bảo hiểm xã hội

#### Định dạng XML

Khi xuất ra file XML, dữ liệu XML11Model được chuyển đổi theo cấu trúc sau:

```xml
<CHI_TIEU_DU_LIEU_GIAY_CHUNG_NHAN_NGHI_VIEC_HUONG_BAO_HIEM_XA_HOI>
  <MA_LK></MA_LK>
  <SO_CT></SO_CT>
  <SO_SERI></SO_SERI>
  <SO_KCB></SO_KCB>
  <DON_VI></DON_VI>
  <MA_BHXH></MA_BHXH>
  <MA_THE_BHYT></MA_THE_BHYT>
  <CHAN_DOAN_RV></CHAN_DOAN_RV>
  <PP_DIEUTRI></PP_DIEUTRI>
  <MA_DINH_CHI_THAI></MA_DINH_CHI_THAI>
  <NGUYENNHAN_DINHCHI></NGUYENNHAN_DINHCHI>
  <TUOI_THAI></TUOI_THAI>
  <SO_NGAY_NGHI></SO_NGAY_NGHI>
  <TU_NGAY></TU_NGAY>
  <DEN_NGAY></DEN_NGAY>
  <HO_TEN_CHA></HO_TEN_CHA>
  <HO_TEN_ME></HO_TEN_ME>
  <MA_TTDV></MA_TTDV>
  <MA_BS></MA_BS>
  <NGAY_CT></NGAY_CT>
  <MA_THE_TAM></MA_THE_TAM>
  <MAU_SO>CT07</MAU_SO>
  <DU_PHONG></DU_PHONG>
</CHI_TIEU_DU_LIEU_GIAY_CHUNG_NHAN_NGHI_VIEC_HUONG_BAO_HIEM_XA_HOI>
```

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `maLK`                    | Mã liên kết                          | String          | 100          | ✅       |
| `soCT`                    | Số chứng từ                          | String          | 200          | ✅       |
| `soSeri`                  | Số seri                              | String          | 200          | ✅       |
| `soKCB`                   | Số khám chữa bệnh                    | String          | 200          | ❌       |
| `donVi`                   | Đơn vị                               | String          | 1024         | ❌       |
| `maBHXH`                  | Mã BHXH                              | String          | 20           | ❌       |
| `maTheBHYT`               | Mã thẻ BHYT                          | String          | n            | ✅       |
| `chanDoanRV`              | Chẩn đoán ra viện                    | String          | n            | ✅       |
| `ppDieuTri`               | Phương pháp điều trị                 | String          | n            | ❌       |
| `maDinhChiThai`           | Mã định chỉ thai                     | Integer         | 1            | ❌       |
| `nguyenNhanDinhChi`       | Nguyên nhân định chỉ                 | String          | n            | ❌       |
| `tuoiThai`                | Tuổi thai (tuần)                     | Integer         | 2            | ❌       |
| `soNgayNghi`              | Số ngày nghỉ                         | Integer         | 3            | ❌       |
| `tuNgay`                  | Từ ngày (yyyyMMdd)                   | String          | 8            | ❌       |
| `denNgay`                 | Đến ngày (yyyyMMdd)                  | String          | 8            | ❌       |
| `hoTenCha`                | Họ tên cha                           | String          | 255          | ❌       |
| `hoTenMe`                 | Họ tên mẹ                            | String          | 255          | ❌       |
| `maTTDV`                  | Mã Thủ trưởng đơn vị                 | String          | 255          | ✅       |
| `maBS`                    | Mã bác sĩ                            | String          | 255          | ❌       |
| `ngayCT`                  | Ngày chứng từ (yyyyMMdd)             | String          | 8            | ✅       |
| `maTheTam`                | Mã thẻ tạm                           | String          | 15           | ❌       |
| `mauSo`                   | Mã mẫu số                            | String          | 10           | ✅       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |

### 12. XML12Model - Chỉ tiêu dữ liệu giám định y khoa

Bảng này lưu trữ thông tin về Chỉ tiêu dữ liệu giám định y khoa

#### Định dạng XML

Khi xuất ra file XML, dữ liệu XML12Model được chuyển đổi theo cấu trúc sau:

```xml
<CHI_TIEU_DU_LIEU_GIAM_DINH_Y_KHOA>
  <NGUOI_CHU_TRI>Nguyễn Văn G</NGUOI_CHU_TRI>
  <CHUC_VU>Chủ tịch hội đồng</CHUC_VU>
  <NGAY_HOP>202101100000</NGAY_HOP>
  <HO_TEN>Trần Văn H</HO_TEN>
  <NGAY_SINH>198001010000</NGAY_SINH>
  <SO_CCCD>012345678903</SO_CCCD>
  <NGAY_CAP_CCCD>20210101</NGAY_CAP_CCCD>
  <NOI_CAP_CCCD>Thành phố ABC</NOI_CAP_CCCD>
  <DIA_CHI>123 Đường DEF, Phường GHI</DIA_CHI>
  <MATINH_CU_TRU>01</MATINH_CU_TRU>
  <MAHUYEN_CU_TRU>001</MAHUYEN_CU_TRU>
  <MAXA_CU_TRU>00001</MAXA_CU_TRU>
  <MA_BHXH>BH123456</MA_BHXH>
  <MA_THE_BHYT>GD4012345678901</MA_THE_BHYT>
  <NGHE_NGHIEP>Lao động</NGHE_NGHIEP>
  <DIEN_THOAI>0912345678</DIEN_THOAI>
  <MA_DOI_TUONG>1</MA_DOI_TUONG>
  <KHAM_GIAM_DINH></KHAM_GIAM_DINH>
  <SO_BIEN_BAN></SO_BIEN_BAN>
  <TYLE_TTCT_CU>20</TYLE_TTCT_CU>
  <DANG_HUONG_CHE_DO>30</DANG_HUONG_CHE_DO>
  <NGAY_CHUNG_TU>50</NGAY_CHUNG_TU>
  <SO_GIAY_GIOI_THIEU>50</SO_GIAY_GIOI_THIEU>
  <NGAY_DE_NGHI>50</NGAY_DE_NGHI>
  <MA_DONVI>50</MA_DONVI>
  <GIOI_THIEU_CUA>50</GIOI_THIEU_CUA>
  <KET_QUA_KHAM>50</KET_QUA_KHAM>
  <SO_VAN_BAN_CAN_CU>50</SO_VAN_BAN_CAN_CU>
  <TYLE_TTCT_MOI>50</TYLE_TTCT_MOI>
  <TONG_TYLE_TTCT>50</TONG_TYLE_TTCT>
  <DANG_KHUYETTAT>50</DANG_KHUYETTAT>
  <MUC_DO_KHUYETTAT>50</MUC_DO_KHUYETTAT>
  <DE_NGHI>50</DE_NGHI>
  <DUOC_XACDINH>50</DUOC_XACDINH>
  <DU_PHONG>DỰ PHÒNG</DU_PHONG>
</CHI_TIEU_DU_LIEU_GIAM_DINH_Y_KHOA>
```

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `nguoiChuTri`             | Người chủ trì                        | String          | 255          | ✅       |
| `chucVu`                  | Chức vụ                              | Integer         | 1            | ❌       |
| `ngayHop`                 | Ngày họp (yyyyMM)                    | String          | 8            | ✅       |
| `hoTen`                   | Họ tên                               | String          | 255          | ✅       |
| `ngaySinh`                | Ngày sinh (yyyyMMdd)                 | String          | 8            | ✅       |
| `soCCCD`                  | Số căn cước công dân                 | String          | n            | ❌       |
| `ngayCapCCCD`             | Ngày cấp CCCD (yyyyMMdd)             | String          | 8            | ❌       |
| `noiCapCCCD`              | Nơi cấp CCCD                         | String          | 1024         | ❌       |
| `diaChi`                  | Địa chỉ                              | String          | 1024         | ❌       |
| `maTinhCuTru`             | Mã tỉnh cư trú                       | String          | 10           | ❌       |
| `maHuyenCuTru`            | Mã huyện cư trú                      | String          | 10           | ❌       |
| `maXaCuTru`               | Mã xã cư trú                         | String          | 10           | ❌       |
| `maBHXH`                  | Mã BHXH                              | String          | 20           | ❌       |
| `maTheBHYT`               | Mã thẻ BHYT                          | String          | 20           | ✅       |
| `ngheNghiep`              | Nghề nghiệp                          | String          | 255          | ❌       |
| `dienThoai`               | Điện thoại                           | String          | 15           | ❌       |
| `maDoiTuong`              | Mã đối tượng                         | String          | 20           | ❌       |
| `khamGiamDinh`            | Khám giám định                       | Integer         | 1            | ❌       |
| `soBienBan`               | Số biên bản                          | String          | 200          | ❌       |
| `tyLeTTCTCu`              | Tỷ lệ thương tật cũ (%)              | Integer         | 3            | ❌       |
| `dangHuongCheDo`          | Dạng hướng chế độ                    | String          | 10           | ❌       |
| `ngayChungTu`             | Ngày chứng từ (yyyyMMdd)             | String          | 8            | ❌       |
| `soGiayGioiThieu`         | Số giấy giới thiệu                   | String          | 200          | ❌       |
| `ngayDeNghi`              | Ngày đề nghị                         | String          | 8            | ❌       |
| `maDonVi`                 | Mã đơn vị                            | String          | 200          | ❌       |
| `gioiThieuCua`            | Giới thiệu của                       | String          | 1024         | ❌       |
| `ketQuaKham`              | Kết quả khám                         | String          | n            | ❌       |
| `soVanBanCanCu`           | Số văn bản căn cứ                    | String          | 200          | ❌       |
| `tyLeTTCTMoi`             | Tỷ lệ thương tật mới (%)             | Integer         | 3            | ❌       |
| `tongTyLeTTCT`            | Tổng tỷ lệ thương tật (%)            | Integer         | 3            | ✅       |
| `dangKhuyetTat`           | Dạng khuyết tật                      | Integer         | 1            | ❌       |
| `mucDoKhuyetTat`          | Mức độ khuyết tật                    | Integer         | 1            | ❌       |
| `deNghi`                  | Đề nghị                              | String          | n            | ❌       |
| `duocXacDinh`             | Được xác định                        | String          | n            | ❌       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |

### 13. XML13Model - Chỉ tiêu dữ liệu giấy chuyển tuyến/chuyển cơ sở khám bệnh, chữa bệnh bảo hiểm y tế

Bảng này lưu trữ thông tin về Chỉ tiêu dữ liệu giấy chuyển tuyến/chuyển cơ sở khám bệnh, chữa bệnh bảo hiểm y tế

#### Định dạng XML

Khi xuất ra file XML, dữ liệu XML13Model được chuyển đổi theo cấu trúc sau:

```xml
<CHI_TIEU_GIAYCHUYENTUYEN>
  <MA_LK></MA_LK>
  <SO_HOSO></SO_HOSO>
  <SO_CHUYENTUYEN></SO_CHUYENTUYEN>
  <GIAY_CHUYEN_TUYEN></GIAY_CHUYEN_TUYEN>
  <MA_CSKCB></MA_CSKCB>
  <MA_NOI_DI></MA_NOI_DI>
  <MA_NOI_DEN></MA_NOI_DEN>
  <HO_TEN></HO_TEN>
  <NGAY_SINH></NGAY_SINH>
  <GIOI_TINH></GIOI_TINH>
  <MA_QUOCTICH></MA_QUOCTICH>
  <MA_DANTOC></MA_DANTOC>
  <MA_NGHE_NGHIEP></MA_NGHE_NGHIEP>
  <DIA_CHI></DIA_CHI>
  <MA_THE_BHYT></MA_THE_BHYT>
  <GT_THE_DEN></GT_THE_DEN>
  <NGAY_VAO></NGAY_VAO>
  <NGAY_VAO_NOI_TRU></NGAY_VAO_NOI_TRU>
  <NGAY_RA></NGAY_RA>
  <DAU_HIEU_LS></DAU_HIEU_LS>
  <CHAN_DOAN_RV></CHAN_DOAN_RV>
  <QT_BENHLY></QT_BENHLY>
  <TOMTAT_KQ></TOMTAT_KQ>
  <PP_DIEUTRI></PP_DIEUTRI>
  <MA_BENH_CHINH></MA_BENH_CHINH>
  <MA_BENH_KT></MA_BENH_KT>
  <MA_BENH_YHCT></MA_BENH_YHCT>
  <PP_DIEU_TRI></PP_DIEU_TRI>
  <MA_LOAI_RV></MA_LOAI_RV>
  <MA_LYDO_CT></MA_LYDO_CT>
  <HUONG_DIEU_TRI></HUONG_DIEU_TRI>
  <PHUONGTIEN_VC></PHUONGTIEN_VC>
  <HOTEN_NGUOI_HT></HOTEN_NGUOI_HT>
  <CHUCDANH_NGUOI_HT></CHUCDANH_NGUOI_HT>
  <MA_BAC_SI></MA_BAC_SI>
  <MA_TTDV></MA_TTDV>
  <DU_PHONG></DU_PHONG>
</CHI_TIEU_GIAYCHUYENTUYEN>
```

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `maLK`                    | Mã liên kết                          | String          | 100          | ✅       |
| `soHoSo`                  | Số hồ sơ                             | String          | 50           | ✅       |
| `soChuyenTuyen`           | Số chuyển tuyến                      | String          | 50           | ✅       |
| `giayChuyenTuyen`         | Giấy chuyển tuyến                    | String          | 50           | ❌       |
| `maCSKCB`                 | Mã cơ sở KCB                         | String          | 10           | ✅       |
| `maNoiDi`                 | Mã nơi đi                            | String          | 100          | ✅       |
| `maNoiDen`                | Mã nơi đến                           | String          | 10           | ✅       |
| `hoTen`                   | Họ tên                               | String          | 255          | ✅       |
| `ngaySinh`                | Ngày sinh (yyyyMMddHHmm)             | String          | 12           | ✅       |
| `gioiTinh`                | Giới tính (1: Nam, 2: Nữ)            | Integer         | 1            | ✅       |
| `maQuocTich`              | Mã quốc tịch                         | String          | 10           | ❌       |
| `maDanToc`                | Mã dân tộc                           | String          | 10           | ❌       |
| `maNgheNghiep`            | Mã nghề nghiệp                       | String          | 10           | ❌       |
| `diaChi`                  | Địa chỉ                              | String          | 1024         | ❌       |
| `maTheBHYT`               | Mã thẻ BHYT                          | String          | n            | ❌       |
| `gtTheDen`                | Giá trị thẻ đến (yyyyMMdd)           | String          | 8            | ❌       |
| `ngayVao`                 | Ngày vào (yyyyMMddHHmm)              | String          | 100          | ❌       |
| `ngayVaoNoiTru`           | Ngày vào nội trú (yyyyMMddHHmm)      | String          | 12           | ❌       |
| `ngayRa`                  | Ngày ra (yyyyMMddHHmm)               | String          | 100          | ❌       |
| `dauHieuLS`               | Dấu hiệu lâm sàng                    | String          | n            | ❌       |
| `chanDoanRV`              | Chẩn đoán ra viện                    | String          | n            | ❌       |
| `qtBenhLy`                | Quá trình bệnh lý                    | String          | n            | ❌       |
| `tomtatKQ`                | Tóm tắt kết quả                      | String          | n            | ❌       |
| `ppDieuTri`               | Phương pháp điều trị                 | String          | n            | ❌       |
| `maBenhChinh`             | Mã bệnh chính                        | String          | 10           | ❌       |
| `maBenhKT`                | Mã bệnh kèm theo                     | String          | 100          | ❌       |
| `maBenhYHCT`              | Mã bệnh y học cổ truyền              | String          | 255          | ❌       |
| `ppDieuTri`               | Phương pháp điều trị                 | String          | n            | ❌       |
| `maLoaiRV`                | Mã loại ra viện                      | Integer         | 1            | ❌       |
| `maLyDoCT`                | Mã lý do chuyển                      | Integer         | 1            | ❌       |
| `huongDieuTri`            | Hướng điều trị                       | String          | n            | ❌       |
| `phuongTienVC`            | Phương tiện vận chuyển               | String          | 255          | ❌       |
| `hoTenNguoiHT`            | Họ tên người hướng dẫn               | String          | 255          | ❌       |
| `chucDanhNguoiHT`         | Chức danh người hướng dẫn            | String          | 255          | ❌       |
| `maBacSi`                 | Mã bác sĩ                            | String          | 255          | ❌       |
| `maTTDV`                  | Mã trung tâm dịch vụ                 | String          | 255          | ❌       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |

### 14. XML14Model - Chỉ tiêu dữ liệu giấy hẹn khám lại

Bảng này lưu trữ thông tin về Chỉ tiêu dữ liệu giấy hẹn khám lại

#### Định dạng XML

Khi xuất ra file XML, dữ liệu XML14Model được chuyển đổi theo cấu trúc sau:

```xml
<CHI_TIEU_GIAYHEN_KHAMLAI>
  <MA_LK></MA_LK>
  <SO_GIAYHEN_KL></SO_GIAYHEN_KL>
  <MA_CSKCB></MA_CSKCB>
  <HO_TEN></HO_TEN>
  <NGAY_SINH></NGAY_SINH>
  <GIOI_TINH></GIOI_TINH>
  <DIA_CHI></DIA_CHI>
  <MA_THE_BHYT></MA_THE_BHYT>
  <GT_THE_DEN></GT_THE_DEN>
  <NGAY_VAO></NGAY_VAO>
  <NGAY_VAO_NOI_TRU></NGAY_VAO_NOI_TRU>
  <NGAY_RA></NGAY_RA>
  <NGAY_HEN_KL></NGAY_HEN_KL>
  <CHAN_DOAN_RV></CHAN_DOAN_RV>
  <MA_BENH_CHINH></MA_BENH_CHINH>
  <MA_BENH_KT></MA_BENH_KT>
  <MA_BENH_YHCT></MA_BENH_YHCT>
  <MA_DOITUONG_KCB></MA_DOITUONG_KCB>
  <MA_BAC_SI></MA_BAC_SI>
  <MA_TTDV></MA_TTDV>
  <NGAY_CT></NGAY_CT>
  <DU_PHONG></DU_PHONG>
</CHI_TIEU_GIAYHEN_KHAMLAI>
```

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `maLK`                    | Mã liên kết                          | String          | 100          | ✅       |
| `soGiayHenKL`             | Số giấy hẹn khám lại                 | String          | 50           | ✅       |
| `maCSKCB`                 | Mã cơ sở KCB                         | String          | 10           | ✅       |
| `hoTen`                   | Họ tên                               | String          | 255          | ✅       |
| `ngaySinh`                | Ngày sinh (yyyyMMddHHmm)             | String          | 12           | ✅       |
| `gioiTinh`                | Giới tính (1: Nam, 2: Nữ)            | Integer         | 1            | ✅       |
| `diaChi`                  | Địa chỉ                              | String          | 1024         | ❌       |
| `maTheBHYT`               | Mã thẻ BHYT                          | String          | n            | ✅       |
| `gtTheDen`                | Giá trị thẻ đến (yyyyMMddHHmm)       | String          | n            | ❌       |
| `ngayVao`                 | Ngày vào (yyyyMMddHHmm)              | String          | 12           | ✅       |
| `ngayVaoNoiTru`           | Ngày vào nội trú (yyyyMMddHHmm)      | String          | 12           | ✅       |
| `ngayRa`                  | Ngày ra (yyyyMMddHHmm)               | String          | 12           | ✅       |
| `ngayHenKL`               | Ngày hẹn khám lại (yyyyMMdd)         | String          | 8            | ✅       |
| `chanDoanRV`              | Chẩn đoán ra viện                    | String          | n            | ✅       |
| `maBenhChinh`             | Mã bệnh chính                        | String          | 10           | ❌       |
| `maBenhKT`                | Mã bệnh kèm theo                     | String          | 100          | ❌       |
| `maBenhYHCT`              | Mã bệnh y học cổ truyền              | String          | 255          | ❌       |
| `maDoiTuongKCB`           | Mã đối tượng KCB                     | String          | 4            | ❌       |
| `maBacSi`                 | Mã bác sĩ                            | String          | 255          | ❌       |
| `maTTDV`                  | Mã trung tâm dịch vụ                 | String          | 255          | ❌       |
| `ngayCT`                  | Ngày chứng từ (yyyyMMdd)             | String          | 8            | ❌       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |

### 15. XML15Model - Thông tin điều trị lao

Bảng này lưu trữ thông tin về điều trị lao, bao gồm thông tin cá nhân, thông tin phân loại lao, thông tin điều trị, thông tin thời gian và thông tin kết quả.

#### Định dạng XML

Khi xuất ra file XML, dữ liệu XML15Model được chuyển đổi theo cấu trúc sau:

```xml
<CHI_TIEU_DIEUTRI_BENHLAO>
  <DSACH_CHITIET_DIEUTRI_BENHLAO>
    <CHITIET_DIEUTRI_BENHLAO>
      <MA_LK></MA_LK>
      <STT></STT>
      <MA_BN></MA_BN>
      <HO_TEN></HO_TEN>
      <SO_CCCD></SO_CCCD>
      <PHANLOAI_LAO_VITRI></PHANLOAI_LAO_VITRI>
      <PHANLOAI_LAO_TS></PHANLOAI_LAO_TS>
      <PHANLOAI_LAO_HIV></PHANLOAI_LAO_HIV>
      <PHANLOAI_LAO_VK></PHANLOAI_LAO_VK>
      <PHANLOAI_LAO_KT></PHANLOAI_LAO_KT>
      <LOAI_DTRI_LAO></LOAI_DTRI_LAO>
      <NGAYBD_DTRI_LAO></NGAYBD_DTRI_LAO>
      <PHACDO_DTRI_LAO></PHACDO_DTRI_LAO>
      <NGAYKT_DTRI_LAO></NGAYKT_DTRI_LAO>
      <KET_QUA_DTRI_LAO></KET_QUA_DTRI_LAO>
      <MA_CSKCB></MA_CSKCB>
      <NGAYKD_HIV></NGAYKD_HIV>
      <BDDT_ARV></BDDT_ARV>
      <NGAY_BAT_DAU_DT_CTX></NGAY_BAT_DAU_DT_CTX>
      <DU_PHONG></DU_PHONG>
    </CHITIET_DIEUTRI_BENHLAO>
    ---- các DSACH_CHITIET_DIEUTRI_BENHLAO khác
  </DSACH_CHITIET_DIEUTRI_BENHLAO>
</CHI_TIEU_DIEUTRI_BENHLAO>
```

| Trường                    | Mô tả                                | Kiểu dữ liệu    | Kích thước   | Bắt buộc |
|---------------------------|--------------------------------------|-----------------|--------------|----------|
| `id`                      | ID tự động tăng                      | Integer         | 11           | ✅       |
| `maLK`                    | Mã liên kết                          | String          | 100          | ✅       |
| `maBN`                    | Mã bệnh nhân                         | String          | 50           | ✅       |
| `hoTen`                   | Họ tên                               | String          | 255          | ✅       |
| `soCCCD`                  | Số căn cước công dân                 | String          | n            | ❌       |
| `phanLoaiLaoViTri`        | Phân loại lao vị trí                 | Integer         | 1            | ❌       |
| `phanLoaiLaoTS`           | Phân loại lao tình trạng             | Integer         | 1            | ❌       |
| `phanLoaiLaoHIV`          | Phân loại lao HIV                    | Integer         | 1            | ❌       |
| `phanLoaiLaoVK`           | Phân loại lao viêm khí quản          | Integer         | 1            | ❌       |
| `phanLoaiLaoKT`           | Phân loại lao kèm theo               | Integer         | 1            | ❌       |
| `loaiDTriLao`             | Loại điều trị lao                    | Integer         | 1            | ✅       |
| `ngayBDDTriLao`           | Ngày bắt đầu điều trị lao (yyyyMMdd) | String          | 8            | ✅       |
| `phacDoDTriLao`           | Phác đồ điều trị lao                 | String          | 50           | ✅       |
| `ngayKTDTriLao`           | Ngày kết thúc điều trị lao (yyyyMMdd)| String          | 8            | ❌       |
| `ketQuaDTriLao`           | Kết quả điều trị lao                 | Integer         | 1            | ❌       |
| `maCSKCB`                 | Mã cơ sở KCB                         | String          | 10           | ✅       |
| `ngayKDHIV`               | Ngày kết thúc điều trị lao (yyyyMMdd)| String          | 8            | ❌       |
| `bddtARV`                 | Ngày bắt đầu điều trị ARV (yyyyMMdd) | String          | 8            | ❌       |
| `ngayBatDauDTCTX`         | Ngày bắt đầu điều trị chất xéa (yyyyMMdd)| String      | 8            | ❌       |
| `duPhong`                 | Dự phòng                             | String          | n            | ❌       |

## Chức năng chính

### 1. Quản lý dữ liệu XML

#### Nhập dữ liệu
- **Upload file XML**: Cho phép tải lên file XML theo định dạng QĐ 4750 thông qua giao diện web
- **Phân tích cú pháp**: Tự động phân tích cấu trúc XML, giải mã Base64 và chuyển đổi thành các đối tượng Django model
- **Hỗ trợ nhiều loại XML**: Xử lý đồng thời 15 loại mẫu XML khác nhau (XML1-XML15)
- **Kiểm tra tính hợp lệ**: Tự động kiểm tra tính hợp lệ của dữ liệu theo quy định của BHXH
- **Xử lý bất đồng bộ**: Xử lý file lớn bằng Django Celery để không chặn giao diện người dùng

#### Xuất dữ liệu
- **Tạo file XML**: Tạo file XML đúng chuẩn theo quy định của QĐ 4750
- **Mã hóa dữ liệu**: Tự động mã hóa dữ liệu theo chuẩn Base64
- **Đóng gói thông tin**: Tổ chức dữ liệu theo cấu trúc chuẩn với các thẻ GIAMDINHHS, THONGTINDONVI, THONGTINHOSO
- **Tải xuống file**: Cho phép tải xuống file XML thông qua HTTP response

#### Lưu trữ và tìm kiếm
- **Lưu vào cơ sở dữ liệu**: Lưu trữ dữ liệu vào cơ sở dữ liệu Django (MySQL/PostgreSQL) để quản lý lâu dài
- **Tìm kiếm nâng cao**: Hỗ trợ tìm kiếm theo nhiều tiêu chí với Django ORM và Django Filter
- **Lọc dữ liệu**: Lọc theo đối tượng khám chữa bệnh, loại hồ sơ, thời gian khám với UI thân thiện
- **Phân trang**: Hiển thị dữ liệu theo trang với Django Pagination để dễ dàng quản lý khi có nhiều bản ghi
- **Tìm kiếm toàn văn**: Tìm kiếm nhanh với Django Full-text Search

### 2. Chỉnh sửa dữ liệu XML

#### Chỉnh sửa thông tin XML
- **Template thống nhất**: Sử dụng template chung `xml_edit.html` cho việc chỉnh sửa tất cả các loại XML
- **Giao diện linh hoạt**: Một template duy nhất hiển thị giao diện phù hợp dựa trên loại XML được chỉnh sửa
- **Form nhập liệu cho XML1 và XML7**: Hiển thị form nhập liệu dạng textbox cho XML1 và XML7
- **DataTables cho XML khác**: Hiển thị dạng danh sách DataTables cho XML2-XML6 và XML8-XML15
- **Chỉnh sửa trực tiếp**: Cho phép chỉnh sửa trực tiếp các cột trong DataTables mà không cần chuyển trang
- **Tự động điền**: Hỗ trợ tự động điền thông tin từ danh mục với Django Select2
- **Kiểm tra định dạng**: Tự động kiểm tra định dạng với Django Form Validation
- **AJAX Form**: Cập nhật dữ liệu không cần tải lại trang với AJAX và Django REST Framework

#### Quản lý danh sách XML
- **Danh sách tổng hợp**: Hiển thị tất cả XML0-XML15 trong một trang `list_4750.html` với khả năng lọc và tìm kiếm
- **Cột cố định**: Cột checkbox và cột hành động được cố định để dễ dàng thao tác
- **DataTables**: Sử dụng DataTables cho tất cả danh sách với các tính năng sắp xếp, tìm kiếm và phân trang
- **Chỉnh sửa nhanh**: Cho phép chỉnh sửa trực tiếp các cột trong bảng mà không cần chuyển trang
- **Thêm/Xóa hàng**: Hỗ trợ thêm và xóa hàng trực tiếp trong DataTables
- **Tính toán tự động**: Tự động tính toán thành tiền, BHTT, BNTT với JavaScript khi thay đổi số lượng hoặc đơn giá
- **Kiểm tra mã**: Xác thực mã thuốc, vật tư y tế, dịch vụ với danh mục chuẩn thông qua AJAX

#### Tính toán lại dữ liệu
- **Tính toán tự động**: Tự động tính toán lại tổng chi thuốc, VTYT, tổng chi BV, tổng chi BH
- **Tính toán phía máy chủ**: Xử lý tính toán phức tạp ở phía máy chủ với Django Signals
- **Cập nhật XML1**: Tự động cập nhật các giá trị tài chính trong XML1 sau khi tính toán

### 3. Kiểm tra thẻ BHYT

#### Quét và đọc thẻ
- **Quét mã QR**: Đọc thông tin từ mã QR trên thẻ BHYT với JavaScript QR Scanner
- **Phân tích dữ liệu**: Tự động phân tích và trích xuất thông tin từ mã QR
- **Hiển thị thông tin**: Hiển thị thông tin cơ bản như họ tên, ngày sinh, mã thẻ

#### Kiểm tra với cơ sở dữ liệu BHXH
- **Kết nối API**: Kết nối với API của BHXH để kiểm tra thông tin thẻ thông qua Django REST Framework
- **Xác thực thông tin**: Kiểm tra tính hợp lệ, thời hạn sử dụng của thẻ
- **Hiển thị kết quả**: Hiển thị kết quả kiểm tra với đầy đủ thông tin trong giao diện web

#### Xem lịch sử khám chữa bệnh
- **Truy vấn lịch sử**: Lấy thông tin lịch sử khám chữa bệnh từ cơ sở dữ liệu BHXH
- **Hiển thị chi tiết**: Hiển thị chi tiết các lần khám, chẩn đoán, chi phí với Django Templates
- **Lọc thông tin**: Cho phép lọc lịch sử theo thời gian, cơ sở KCB với Django Filter

### 4. Báo cáo và thống kê

#### Báo cáo BHXH
- **Tạo báo cáo**: Tạo báo cáo theo mẫu quy định của BHXH với Django Templates
- **Tùy chỉnh thời gian**: Cho phép chọn khoảng thời gian báo cáo với Django DateRangePicker
- **Tùy chỉnh đối tượng**: Cho phép chọn đối tượng báo cáo với Django Select2

#### Xuất báo cáo
- **Xuất Excel**: Hỗ trợ xuất báo cáo ra file Excel với thư viện openpyxl
- **Xuất PDF**: Hỗ trợ xuất báo cáo ra file PDF với WeasyPrint
- **Tùy chỉnh định dạng**: Cho phép tùy chỉnh định dạng, bố cục của báo cáo
- **Lập lịch báo cáo**: Tự động tạo và gửi báo cáo theo lịch với Django Celery

#### Xuất dữ liệu ra Excel

Chức năng xuất dữ liệu ra Excel cung cấp nhiều tùy chọn và định dạng để đáp ứng các yêu cầu báo cáo khác nhau:

##### Định dạng và tùy chỉnh
- **Định dạng chuẩn**: Xuất dữ liệu theo định dạng chuẩn của Bộ Y tế và BHXH
- **Tùy chỉnh cột**: Cho phép chọn các cột cần xuất, thay đổi thứ tự hiển thị
- **Định dạng ô**: Hỗ trợ định dạng ô (font, màu sắc, viền, căn lề) theo mẫu báo cáo
- **Định dạng số**: Tự động định dạng số (số nguyên, số thập phân, tiền tệ) theo quy định
- **Định dạng ngày tháng**: Chuyển đổi định dạng ngày tháng từ yyyyMMddHHmm sang dd/MM/yyyy hoặc các định dạng khác
- **Gộp ô**: Hỗ trợ gộp ô cho tiêu đề, nhóm dữ liệu liên quan
- **Tự động điều chỉnh cột**: Tự động điều chỉnh độ rộng cột phù hợp với nội dung

##### Cấu trúc báo cáo Excel
Ứng dụng hỗ trợ các loại báo cáo Excel với cấu trúc cụ thể như sau:

###### 1. Báo cáo 3360 - Tổng hợp chi phí khám chữa bệnh
- **Cấu trúc file**: Bảng dữ liệu với các cột theo mẫu 3360 của BHXH
- **Cấu trúc header**:
  ```
  STT, MA_BN, HO_TEN, NGAY_SINH, GIOI_TINH, DIA_CHI, MA_THE, MA_DKBD, GT_THE_TU, GT_THE_DEN,
  MA_BENH, MA_BENHKHAC, MA_LYDO_VVIEN, MA_NOI_CHUYEN, NGAY_VAO, NGAY_RA, SO_NGAY_DTRI,
  KET_QUA_DTRI, TINH_TRANG_RV, T_TONGCHI, T_XN, T_CDHA, T_THUOC, T_MAU, T_PTTT, T_VTYT,
  T_DVKT_TYLE, T_THUOC_TYLE, T_VTYT_TYLE, T_KHAM, T_GIUONG, T_VCHUYEN, T_BNTT, T_BHTT,
  T_NGOAIDS, MA_KHOA, NAM_QT, THANG_QT, MA_KHUVUC, MA_LOAIKCB, MA_CSKCB, T_NGUONKHAC
  ```
- **Mapping dữ liệu**:
  - Thông tin cá nhân: `maBN`, `hoTen`, `ngaySinh`, `gioiTinh`, `diaChi`, `maTheBHYT`, `maDKBD`
  - Thông tin BHYT: `gtTheTu`, `gtTheDen`, `maBenh`, `maBenhKhac`, `maLyDoVVien`, `maNoiChuyen`
  - Thông tin điều trị: `ngayVao`, `ngayRa`, `soNgayDtri`, `ketQuaDtri`, `tinhTrangRV`
  - Thông tin chi phí: `tTongChi`, `tXN`, `tCDHA`, `tThuoc`, `tMau`, `tPTTT`, `tVTYT`, `tDVKT_TyLe`, `tThuoc_TyLe`, `tVTYT_TyLe`, `tKham`, `tGiuong`, `tVChuyen`, `tBNTT`, `tBNCCT`, `tBHTT`, `tNguonKhac`
  - Thông tin khác: `maKhoa`, `namQT`, `thangQT`, `maKhuVuc`, `maLoaiKCB`, `maCSKCB`

###### 2. Báo cáo 102 - Tổng hợp chi phí theo Thông tư 102
- **Cấu trúc file**: Báo cáo theo mẫu C79-HD của Thông tư 102/2018/TT-BYT
- **Cấu trúc header**:
  ```
  STT, Họ và tên, Năm sinh, Giới tính, Mã thẻ BHYT, Mã bệnh, Ngày vào, Ngày ra, Số ngày điều trị,
  Tổng cộng, Khám bệnh, Ngày giường, Xét nghiệm, CĐHA TDCN, Thủ thuật/Phẫu thuật, Máu, Thuốc dịch,
  VTYT, Vận chuyển, Tại tỉnh/thành phố, Tại Trung ương, NĐ 70, Cùng chi trả, Tự trả, NSĐP,
  Hỗ trợ/tài trợ, Chi phí ngoài phạm vi BHYT
  ```
- **Phân nhóm dữ liệu**:
  - Khám chữa bệnh ngoại trú:
    - Đối tượng theo Nghị định 146: Người bệnh ĐKBĐ tại cơ sở KCB, Người bệnh trong tỉnh đến, Bệnh nhân ngoại tỉnh đến
    - Đối tượng theo Nghị định 70: Bệnh nhân ngoại tỉnh đến
  - Điều trị nội trú:
    - Đối tượng theo Nghị định 146: Người bệnh ĐKBĐ tại cơ sở KCB, Người bệnh trong tỉnh đến, Bệnh nhân ngoại tỉnh đến
    - Đối tượng theo Nghị định 70: Bệnh nhân ngoại tỉnh đến
- **Mapping dữ liệu**:
  - Thông tin cá nhân: `hoTen`, `ngaySinh`, `gioiTinh`, `maTheBHYT`, `maBenh`
  - Thông tin điều trị: `ngayVao`, `ngayRa`, `soNgayDtri`
  - Chi phí trong phạm vi BHYT: `tTongChi`, `tKham`, `tGiuong`, `tXN`, `tCDHA`, `tPTTT`, `tMau`, `tThuoc`, `tVTYT`, `tVChuyen`
  - Quỹ BHYT trả: Tại tỉnh/thành phố (`tBHTT`), Tại Trung ương (0), NĐ 70 (0)
  - Người bệnh: Cùng chi trả (`tBNCCT`), Tự trả (`tBNTT`)
  - Nguồn khác: NSĐP (`tNgoaids`), Hỗ trợ/tài trợ (0)
  - Chi phí ngoài phạm vi BHYT (0)

###### 3. Báo cáo chấm công
- **Cấu trúc file**: Hai sheet - "Ngày y lệnh" và "Thực hành YL"
- **Sheet Ngày y lệnh**:
  - **Cấu trúc header**:
    ```
    STT, Họ và tên, Mã CCHN, Ngày trong tháng (31 cột từ 1-31), Cộng, Ghi chú
    ```
  - Thông tin bác sĩ: STT, Họ và tên, Mã CCHN
  - Ngày trong tháng: 31 cột tương ứng với các ngày trong tháng (đánh dấu "x" nếu có y lệnh)
  - Tổng cộng: Tổng số ngày làm việc (công thức đếm số ô có giá trị "x")
- **Sheet Thực hành YL**:
  - **Cấu trúc header**:
    ```
    STT, Họ và tên, Mã CCHN, Ngày trong tháng (31 cột từ 1-31), Cộng, Ghi chú
    ```
  - Thông tin bác sĩ: STT, Họ và tên, Mã CCHN
  - Ngày trong tháng: 31 cột tương ứng với các ngày trong tháng (đánh dấu "x" nếu có thực hành y lệnh)
  - Tổng cộng: Tổng số ngày thực hành y lệnh (công thức đếm số ô có giá trị "x")
- **Mapping dữ liệu**:
  - Từ XML2Model: `ngayYL`, `maBacSi`
  - Từ XML3Model: `ngayYL`, `maBacSi`, `ngayTHYL`, `nguoiThucHien`

###### 4. Báo cáo 19 - Báo cáo vật tư y tế
- **Cấu trúc file**: Bảng dữ liệu với thông tin chi tiết về vật tư y tế
- **Cấu trúc header**:
  ```
  STT, Mã VTYT, Tên VTYT, Tên thương mại, Quy cách, Đơn vị, Giá mua, SL nội trú, SL ngoại trú, Giá thanh toán, Thành tiền
  ```
- **Mapping dữ liệu**:
  - Thông tin VTYT: `maVatTu`, `tenVatTu`, `tenThuongMai`, `quyCache`, `donVi`
  - Thông tin sử dụng: `slNoiTru`, `slNgoaiTru`
  - Thông tin giá: `giaMua`, `giaThanhToan`, `thanhTien`
- **Phân loại dữ liệu**: Theo loại KCB (nội trú/ngoại trú)

###### 5. Báo cáo 20 - Báo cáo thuốc
- **Cấu trúc file**: Bảng dữ liệu với thông tin chi tiết về thuốc
- **Cấu trúc header**:
  ```
  STT, Mã thuốc, Tên hoạt chất, Tên thuốc, Đường dùng, Hàm lượng, Số đăng ký, Đơn vị, SL nội trú, SL ngoại trú, Đơn giá, Thành tiền
  ```
- **Mapping dữ liệu**:
  - Thông tin thuốc: `maThuoc`, `tenHoatChat`, `tenThuoc`, `duongDung`, `hamLuong`, `soDKY`, `donVi`
  - Thông tin sử dụng: `slNoiTru`, `slNgoaiTru`
  - Thông tin giá: `donGia`, `thanhTien`
- **Phân loại dữ liệu**: Theo loại KCB (nội trú/ngoại trú)

###### 6. Báo cáo 21 - Báo cáo dịch vụ kỹ thuật
- **Cấu trúc file**: Bảng dữ liệu với thông tin chi tiết về dịch vụ kỹ thuật
- **Cấu trúc header**:
  ```
  STT, Mã DVKT, Tên DVKT, SL nội trú, SL ngoại trú, Đơn giá, Thành tiền
  ```
- **Mapping dữ liệu**:
  - Thông tin DVKT: `maDichVu`, `tenDichVu`
  - Thông tin sử dụng: `slNoiTru`, `slNgoaiTru`
  - Thông tin giá: `donGia`, `thanhTien`
- **Phân loại dữ liệu**: Theo loại KCB (nội trú/ngoại trú)

##### Tính năng nâng cao
- **Lọc dữ liệu**: Cho phép lọc dữ liệu trước khi xuất (theo thời gian, đối tượng, loại KCB)
- **Tính toán tự động**: Tự động tính toán tổng, trung bình, phần trăm trong báo cáo
- **Biểu đồ**: Tự động tạo biểu đồ (cột, đường, tròn) từ dữ liệu được chọn
- **Nhiều sheet**: Hỗ trợ xuất dữ liệu ra nhiều sheet trong cùng một file Excel
- **Mẫu báo cáo**: Hỗ trợ lưu và sử dụng lại các mẫu báo cáo đã tạo
- **Xuất theo lô**: Cho phép xuất nhiều báo cáo cùng lúc theo lô
- **Bảo vệ dữ liệu**: Tùy chọn bảo vệ sheet/workbook để tránh chỉnh sửa không mong muốn
- **Chèn logo và header/footer**: Hỗ trợ chèn logo đơn vị, thông tin header/footer vào báo cáo

##### Quy trình xuất Excel
1. **Chọn loại báo cáo**: Người dùng chọn loại báo cáo cần xuất
2. **Cấu hình báo cáo**: Tùy chỉnh các thông số (cột, định dạng, lọc dữ liệu)
3. **Xem trước**: Xem trước báo cáo trước khi xuất
4. **Xuất file**: Xuất ra file Excel và lưu vào vị trí được chỉ định
5. **Mở file**: Tùy chọn tự động mở file sau khi xuất

#### Thống kê và phân tích
- **Thống kê chi phí**: Thống kê chi phí theo nhiều tiêu chí (thuốc, VTYT, dịch vụ)
- **Thống kê bệnh nhân**: Thống kê số lượng bệnh nhân theo đối tượng, độ tuổi, giới tính
- **Biểu đồ trực quan**: Hiển thị dữ liệu thống kê dưới dạng biểu đồ trực quan

### 5. Gửi dữ liệu lên cổng BHXH

#### Chuẩn bị dữ liệu
- **Kiểm tra tính hợp lệ**: Kiểm tra tính đầy đủ và hợp lệ của dữ liệu trước khi gửi
- **Định dạng XML**: Định dạng dữ liệu theo chuẩn XML của QĐ 4750
- **Mã hóa dữ liệu**: Mã hóa dữ liệu theo yêu cầu của cổng tiếp nhận BHXH

#### Gửi dữ liệu
- **Kết nối API**: Kết nối với API của cổng tiếp nhận BHXH
- **Xác thực**: Xác thực người dùng với tài khoản và mật khẩu được cấp
- **Gửi dữ liệu**: Gửi dữ liệu XML lên cổng tiếp nhận qua phương thức POST

#### Theo dõi và xử lý kết quả
- **Nhận phản hồi**: Nhận và phân tích phản hồi từ cổng tiếp nhận
- **Hiển thị kết quả**: Hiển thị kết quả gửi dữ liệu (thành công, thất bại, lỗi)
- **Lưu trạng thái**: Cập nhật trạng thái gửi trong cơ sở dữ liệu để theo dõi

## Cấu trúc file XML

File XML theo Quyết định 4750 có cấu trúc tổng thể như sau:

```xml
<GIAMDINHHS>
  <THONGTINDONVI>
    <MACSKCB>...</MACSKCB>
  </THONGTINDONVI>
  <THONGTINHOSO>
    <NGAYLAP>...</NGAYLAP>
    <SOLUONGHOSO>...</SOLUONGHOSO>
    <DANHSACHHOSO>
      <HOSO>
        <FILEHOSO>
          <LOAIHOSO>XML1</LOAIHOSO>
          <NOIDUNGFILE><!-- Chuỗi Base64 chứa dữ liệu XML1 --></NOIDUNGFILE>
        </FILEHOSO>
        <FILEHOSO>
          <LOAIHOSO>XML2</LOAIHOSO>
          <NOIDUNGFILE><!-- Chuỗi Base64 chứa dữ liệu XML2 --></NOIDUNGFILE>
        </FILEHOSO>
        <!-- Các FILEHOSO khác (XML3-XML15) -->
      </HOSO>
      <!-- Các HOSO khác -->
    </DANHSACHHOSO>
  </THONGTINHOSO>
</GIAMDINHHS>
```

Mỗi hồ sơ (`HOSO`) có thể chứa nhiều loại file (`FILEHOSO`) tương ứng với các model XML1 đến XML15. Nội dung của mỗi file được mã hóa Base64 trong thẻ `NOIDUNGFILE`.

## Quy trình làm việc trong Django

### 1. Nhập dữ liệu XML
- **Upload file XML**: Sử dụng form tải lên Django để tải file XML theo định dạng QĐ 4750
- **Xử lý bất đồng bộ**: Hệ thống xử lý file lớn bằng Celery task để không chặn giao diện người dùng
- **Giải mã và phân tích**: Hệ thống tự động giải mã Base64, phân tích cú pháp XML và chuyển đổi thành các đối tượng Django model
- **Hiển thị dữ liệu**: Dữ liệu được hiển thị trong các bảng dữ liệu với Django Templates và DataTables
- **Thông báo kết quả**: Sử dụng Django Messages Framework để thông báo kết quả xử lý

### 2. Kiểm tra và chỉnh sửa dữ liệu
- **Xem và chỉnh sửa**: Sử dụng Django ModelForms để mở form chỉnh sửa chi tiết
- **Tính toán lại**: Sử dụng Django Signals và JavaScript để tính toán lại các giá trị tài chính
- **Kiểm tra tính hợp lệ**: Hệ thống tự động kiểm tra tính hợp lệ của dữ liệu với Django Form Validation
- **Lưu trữ phiên bản**: Theo dõi lịch sử thay đổi với Django-Reversion
- **Xử lý đồng thời**: Xử lý nhiều người dùng cùng chỉnh sửa với Django Concurrency Control

### 3. Lưu trữ và xuất dữ liệu
- **Lưu vào cơ sở dữ liệu**: Sử dụng Django ORM để lưu dữ liệu vào cơ sở dữ liệu
- **Xuất ra file XML**: Sử dụng Django View để tạo và tải xuống file XML
- **Quy trình xuất XML**:
  1. Truy vấn dữ liệu từ cơ sở dữ liệu với Django ORM
  2. Tạo cấu trúc XML tổng thể (`GIAMDINHHS`) với ElementTree
  3. Thêm thông tin đơn vị (`THONGTINDONVI`)
  4. Tạo danh sách hồ sơ (`DANHSACHHOSO`)
  5. Chuyển đổi từng model thành chuỗi XML
  6. Mã hóa Base64 và đóng gói trong cấu trúc `FILEHOSO`
  7. Trả về HttpResponse với Content-Type và Content-Disposition phù hợp

### 4. Gửi dữ liệu và theo dõi
- **Gửi lên cổng BHXH**: Sử dụng Django REST Framework để gửi dữ liệu lên cổng tiếp nhận BHXH
- **Quy trình gửi XML**:
  1. Tạo file XML hoàn chỉnh
  2. Mã hóa Base64 toàn bộ file
  3. Gửi lên API của BHXH qua phương thức POST với requests
  4. Nhận và xử lý phản hồi từ hệ thống
- **Theo dõi trạng thái**: Cập nhật trạng thái gửi trong cơ sở dữ liệu với Django model và hiển thị cho người dùng
- **Lịch sử gửi dữ liệu**: Lưu trữ lịch sử gửi dữ liệu và phản hồi từ BHXH
- **Thông báo kết quả**: Gửi email thông báo kết quả với Django Email

## Cài đặt và cấu hình

### Yêu cầu hệ thống

- **Hệ điều hành**: Windows/Linux/macOS (hỗ trợ đa nền tảng)
- **Python**: Phiên bản 3.8 trở lên (khuyến nghị Python 3.10)
- **Cơ sở dữ liệu**: MySQL 5.7+ hoặc PostgreSQL 12+
- **Web server**: Nginx hoặc Apache với mod_wsgi
- **Thư viện chính**:
  - Django 4.2+: Framework web
  - Django REST Framework: API RESTful
  - Celery: Xử lý tác vụ bất đồng bộ
  - Redis: Message broker cho Celery
  - ElementTree: Xử lý XML
  - Requests: Gửi và nhận dữ liệu từ API
  - WeasyPrint: Tạo file PDF
  - openpyxl: Xử lý file Excel
- **Kết nối mạng**: Cần kết nối internet để gửi dữ liệu lên cổng BHXH
- **Phần cứng tối thiểu (máy chủ)**:
  - CPU: 4 nhân, 2.0 GHz trở lên
  - RAM: 8GB trở lên
  - Ổ cứng: 10GB không gian trống

### Cài đặt

#### Tích hợp vào hệ thống quản lý bệnh viện hiện có

1. **Tạo ứng dụng Django mới trong hệ thống hiện có**:
   ```bash
   cd quanlybv
   python manage.py startapp xml4750
   ```

2. **Tạo các file cần thiết cho ứng dụng**:
   ```bash
   cd xml4750
   touch models.py views.py forms.py urls.py utils.py
   ```

3. **Tạo thư mục templates và static cho ứng dụng**:
   ```bash
   mkdir -p ../templates/xml4750
   mkdir -p ../static/xml4750/css ../static/xml4750/js ../static/xml4750/images
   ```

4. **Cài đặt các thư viện phụ thuộc**:
   ```bash
   pip install requests celery redis openpyxl weasyprint
   ```

5. **Thêm ứng dụng vào INSTALLED_APPS trong settings.py**:
   ```python
   INSTALLED_APPS = [
       # Các ứng dụng hiện có
       'danhmuc130',
       'danhmucbv',
       'departments',
       'devices',
       'emails',
       'permissions',
       'reports',
       'schedules',
       'typingpractice',
       'users',
       # Thêm ứng dụng mới
       'xml4750',
   ]
   ```

6. **Cấu hình URL routing trong urls.py chính**:
   ```python
   urlpatterns = [
       # Các URL hiện có
       path('admin/', admin.site.urls),
       path('danhmucbv/', include('danhmucbv.urls')),
       path('reports/', include('reports.urls')),
       # Thêm URL cho ứng dụng mới
       path('xml4750/', include('xml4750.urls')),
   ]
   ```

7. **Chạy migration để tạo bảng dữ liệu mới**:
   ```bash
   python manage.py makemigrations xml4750
   python manage.py migrate
   ```

8. **Cập nhật sidebar menu trong templates/layouts/sidebar.html**:
   Thêm mục menu cho ứng dụng XML4750 vào sidebar

#### Cấu hình Celery cho xử lý bất đồng bộ

1. **Cấu hình Celery trong settings.py**:
   ```python
   # Cấu hình Celery
   CELERY_BROKER_URL = 'redis://localhost:6379/0'
   CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
   CELERY_ACCEPT_CONTENT = ['json']
   CELERY_TASK_SERIALIZER = 'json'
   CELERY_RESULT_SERIALIZER = 'json'
   ```

2. **Tạo file celery.py trong thư mục cấu hình Django chính**:
   ```python
   # quanlybv/celery.py
   import os
   from celery import Celery

   os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlybv.settings')

   app = Celery('quanlybv')
   app.config_from_object('django.conf:settings', namespace='CELERY')
   app.autodiscover_tasks()
   ```

3. **Cập nhật file __init__.py trong thư mục cấu hình Django chính**:
   ```python
   # quanlybv/__init__.py
   from .celery import app as celery_app

   __all__ = ('celery_app',)
   ```

4. **Tạo file tasks.py trong ứng dụng xml4750**:
   ```python
   # apps/xml4750/tasks.py
   from celery import shared_task

   @shared_task
   def process_xml_file(file_path):
       # Xử lý file XML
       pass
   ```

#### Triển khai lên môi trường sản xuất

1. **Cập nhật cấu hình Nginx hiện có**:
   Thêm cấu hình cho các URL của ứng dụng xml4750

2. **Cấu hình Supervisor cho Celery worker**:
   Tạo file cấu hình Supervisor để quản lý Celery worker

3. **Cập nhật cấu hình HTTPS**:
   Đảm bảo các URL mới được bảo vệ bởi HTTPS

### Cấu hình

#### Cấu hình Django

1. **Cấu hình cơ sở dữ liệu trong `settings.py`**:
   ```python
   DATABASES = {
       'default': {
           'ENGINE': 'django.db.backends.mysql',
           'NAME': 'quanlyxml',
           'USER': 'quanlyxml_user',
           'PASSWORD': 'your_password',
           'HOST': 'localhost',
           'PORT': '3306',
           'OPTIONS': {
               'charset': 'utf8mb4',
           }
       }
   }
   ```

2. **Cấu hình Celery**:
   ```python
   # settings.py
   CELERY_BROKER_URL = 'redis://localhost:6379/0'
   CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
   CELERY_ACCEPT_CONTENT = ['json']
   CELERY_TASK_SERIALIZER = 'json'
   CELERY_RESULT_SERIALIZER = 'json'
   ```

#### Cấu hình kết nối BHXH

1. **Cấu hình thông tin kết nối cổng BHXH trong `settings.py`**:
   ```python
   # settings.py
   BHXH_API = {
       'SERVER_URL': 'https://địa_chỉ_máy_chủ_BHXH/api',
       'USERNAME': 'tên_đăng_nhập_BHXH',
       'PASSWORD': 'mật_khẩu_BHXH',
       'MA_CSKCB': 'mã_cơ_sở_KCB',
       'TIMEOUT': 30,  # Thời gian timeout (giây)
   }
   ```

2. **Cấu hình thông tin cơ sở KCB trong Django Admin**:
   - Đăng nhập vào Django Admin
   - Truy cập mục "Cấu hình hệ thống"
   - Cập nhật thông tin cơ sở KCB

### Khởi động ứng dụng

1. **Khởi động trong môi trường phát triển**:
   ```bash
   # Khởi động Django server
   python manage.py runserver

   # Khởi động Celery worker (cửa sổ terminal khác)
   celery -A quanlyxml worker -l info
   ```

2. **Khởi động trong môi trường sản xuất**:
   ```bash
   # Khởi động Nginx
   sudo systemctl start nginx

   # Khởi động Supervisor (quản lý Gunicorn và Celery)
   sudo systemctl start supervisor
   ```

3. **Truy cập ứng dụng**:
   - Mở trình duyệt và truy cập địa chỉ: `http://localhost:8000` (phát triển) hoặc tên miền đã cấu hình (sản xuất)

## Tài liệu tham khảo

### Tài liệu chính thức

- [Quyết định 4750/QĐ-BYT](https://thuvienphapluat.vn/van-ban/Bao-hiem/Quyet-dinh-4750-QD-BYT-2018-trien-khai-ung-dung-cong-nghe-thong-tin-quan-ly-kham-chua-benh-BHYT-394152.aspx) - Quyết định về việc triển khai ứng dụng công nghệ thông tin trong quản lý khám bệnh, chữa bệnh và thanh toán bảo hiểm y tế
- [Cổng thông tin điện tử BHXH Việt Nam](https://baohiemxahoi.gov.vn/) - Cổng thông tin chính thức của Bảo hiểm Xã hội Việt Nam
- [Cổng tiếp nhận dữ liệu BHYT](https://gdbhyt.baohiemxahoi.gov.vn/) - Cổng tiếp nhận dữ liệu khám chữa bệnh BHYT

### Tài liệu kỹ thuật

- [Tài liệu hướng dẫn định dạng XML](https://gdbhyt.baohiemxahoi.gov.vn/tai-lieu) - Tài liệu hướng dẫn định dạng XML theo quy định của BHXH
- [Tài liệu API cổng tiếp nhận](https://gdbhyt.baohiemxahoi.gov.vn/api-docs) - Tài liệu hướng dẫn sử dụng API của cổng tiếp nhận BHXH
- [Danh mục dùng chung](https://gdbhyt.baohiemxahoi.gov.vn/danh-muc) - Danh mục dùng chung trong lĩnh vực y tế và bảo hiểm y tế

### Thư viện và công nghệ

- [Python](https://www.python.org/) - Ngôn ngữ lập trình Python
- [Django](https://www.djangoproject.com/) - Framework web Python
- [Django REST Framework](https://www.django-rest-framework.org/) - Framework API RESTful cho Django
- [Celery](https://docs.celeryq.dev/) - Hệ thống xử lý tác vụ bất đồng bộ
- [Redis](https://redis.io/) - Hệ thống lưu trữ dữ liệu trong bộ nhớ
- [MySQL](https://www.mysql.com/) - Hệ quản trị cơ sở dữ liệu MySQL
- [PostgreSQL](https://www.postgresql.org/) - Hệ quản trị cơ sở dữ liệu PostgreSQL
- [Nginx](https://nginx.org/) - Web server
- [Gunicorn](https://gunicorn.org/) - WSGI HTTP Server cho Python
- [WeasyPrint](https://weasyprint.org/) - Thư viện tạo file PDF từ HTML/CSS
- [openpyxl](https://openpyxl.readthedocs.io/) - Thư viện xử lý file Excel
- [Bootstrap](https://getbootstrap.com/) - Framework CSS
- [jQuery](https://jquery.com/) - Thư viện JavaScript
- [DataTables](https://datatables.net/) - Plugin jQuery cho bảng dữ liệu
- [Select2](https://select2.org/) - Plugin jQuery cho dropdown nâng cao

## Kết luận

Module Quản lý dữ liệu XML theo Quyết định 4750 là một giải pháp tích hợp toàn diện giúp các cơ sở y tế quản lý, xử lý và gửi dữ liệu khám chữa bệnh BHYT một cách hiệu quả và chính xác. Với việc tích hợp liền mạch vào hệ thống quản lý bệnh viện hiện có, module này mang lại nhiều lợi ích đáng kể:

- **Tận dụng dữ liệu hiện có**: Sử dụng trực tiếp dữ liệu từ hệ thống quản lý bệnh viện, tránh nhập liệu trùng lặp
- **Giao diện thống nhất**: Người dùng không cần làm quen với giao diện mới, giảm thời gian đào tạo
- **Quản lý tập trung**: Quản lý tất cả dữ liệu trong một hệ thống duy nhất, tăng tính nhất quán
- **Tối ưu hóa quy trình**: Tự động hóa quy trình từ khám chữa bệnh đến thanh toán BHYT
- **Tiết kiệm chi phí**: Không cần đầu tư hệ thống mới, tận dụng cơ sở hạ tầng hiện có
- **Bảo mật đồng bộ**: Áp dụng các chính sách bảo mật thống nhất trên toàn hệ thống
- **Phân quyền chi tiết**: Tận dụng hệ thống phân quyền hiện có, đảm bảo kiểm soát truy cập chặt chẽ
- **Báo cáo tích hợp**: Tạo báo cáo tổng hợp từ nhiều nguồn dữ liệu trong hệ thống

Module được phát triển dựa trên Django - một framework web Python mạnh mẽ, tuân thủ các quy định của Bộ Y tế và Bảo hiểm Xã hội Việt Nam, đảm bảo tính chính xác, an toàn và bảo mật của dữ liệu. Với kiến trúc tích hợp, module mang lại nhiều lợi ích vượt trội so với giải pháp độc lập:

- **Đồng bộ dữ liệu tự động**: Dữ liệu được đồng bộ tự động giữa các phần của hệ thống
- **Trải nghiệm người dùng nhất quán**: Giao diện và luồng công việc thống nhất trên toàn hệ thống
- **Quản lý người dùng tập trung**: Một hệ thống quản lý người dùng duy nhất cho toàn bộ ứng dụng
- **Bảo trì và nâng cấp dễ dàng**: Cập nhật module mà không ảnh hưởng đến hệ thống hiện có
- **Mở rộng linh hoạt**: Dễ dàng thêm tính năng mới theo yêu cầu thay đổi của quy định

Với khả năng tích hợp liền mạch và cập nhật linh hoạt, module này là giải pháp lý tưởng để đáp ứng các yêu cầu về quản lý dữ liệu XML theo Quyết định 4750 mà không cần thay đổi lớn đối với hệ thống quản lý bệnh viện hiện có.

### Liên hệ và hỗ trợ

Nếu bạn có bất kỳ câu hỏi, góp ý hoặc cần hỗ trợ, vui lòng liên hệ với chúng tôi qua:

- **Email**: <EMAIL>
- **Điện thoại**: 0123 456 789
- **Website**: https://quanlyxml.com
- **Hỗ trợ trực tuyến**: Hệ thống chat trực tuyến tích hợp trong ứng dụng
- **Tài liệu**: Tài liệu hướng dẫn sử dụng chi tiết tại https://docs.quanlyxml.com
